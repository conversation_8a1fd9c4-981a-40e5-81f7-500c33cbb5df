@echo off
chcp 65001 >nul
echo 🚀 康多远程手术机器人监控系统 - 快速打包
echo ==========================================

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 📦 检查PyInstaller...
python -c "import PyInstaller; print('✅ PyInstaller已安装')" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
)

echo 🔨 开始打包...
echo 🔧 使用优化参数防止控制台弹窗...
pyinstaller --onefile --windowed --noconsole --name="康多远程手术监控系统" remote_surgery_interface.py

if exist "dist\康多远程手术监控系统.exe" (
    echo ✅ 打包成功!
    echo 📁 生成的文件: dist\康多远程手术监控系统.exe
    
    echo 📏 文件信息:
    dir "dist\康多远程手术监控系统.exe"
    
    echo.
    echo 🎉 打包完成! 
    echo 💡 提示: exe文件位于 dist 目录中
    echo 💡 该文件可在没有Python环境的电脑上运行
) else (
    echo ❌ 打包失败，请检查错误信息
)

echo.
pause
