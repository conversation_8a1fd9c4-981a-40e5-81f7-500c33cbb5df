#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pyqtgraph as pg
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QColor

class ChartManager:
    """图表管理类"""
    
    def __init__(self, graph_widget, language_manager):
        self.graph_widget = graph_widget
        self.language_manager = language_manager
        self.ping_data = []
        self.timestamps = []
        self.y_range_min = 0
        self.y_range_max = 50  # 初始范围
        self.auto_y_range = True  # 是否自动调整Y轴范围
        self.manual_y_max = 200  # 手动设置的Y轴最大值
        
        self.setup_chart()
        
    def setup_chart(self):
        """设置图表"""
        # 设置图表样式
        self.graph_widget.setBackground('#ffffff')
        self.graph_widget.showGrid(x=True, y=True, alpha=0.3)
        
        # 设置X轴和Y轴标签
        self.graph_widget.setLabel('left', self.language_manager.get_text('latency_ms'))
        self.graph_widget.setLabel('bottom', self.language_manager.get_text('time_s'))
        
        # 创建曲线
        self.curve = self.graph_widget.plot(pen=pg.mkPen(color='#3498db', width=3))
        
        # 创建填充区域
        self.fill_curve = pg.FillBetweenItem(
            pg.PlotCurveItem(), 
            pg.PlotCurveItem(pen=pg.mkPen(color='#3498db', width=0)),
            brush=pg.mkBrush(QColor(52, 152, 219, 50))
        )
        self.graph_widget.addItem(self.fill_curve)
        
        # 创建阈值线
        self.threshold_line_120 = pg.InfiniteLine(
            pos=120, angle=0, 
            pen=pg.mkPen(color='#f39c12', width=1, style=Qt.DashLine),
            label='120ms', labelOpts={'position': 0.9, 'color': '#f39c12', 'fill': '#ffffff'}
        )
        self.threshold_line_150 = pg.InfiniteLine(
            pos=150, angle=0, 
            pen=pg.mkPen(color='#e74c3c', width=1, style=Qt.DashLine),
            label='150ms', labelOpts={'position': 0.9, 'color': '#e74c3c', 'fill': '#ffffff'}
        )
        self.graph_widget.addItem(self.threshold_line_120)
        self.graph_widget.addItem(self.threshold_line_150)
        
        # 禁用鼠标交互功能，确保图表能实时更新
        self.graph_widget.setMouseEnabled(x=False, y=False)
        self.graph_widget.getViewBox().setMouseEnabled(x=False, y=False)
        self.graph_widget.getViewBox().setMenuEnabled(False)
        
    def update_chart_data(self, latency, loss, current_time):
        """更新图表数据"""
        if latency is not None:
            # 添加新的数据点
            self.ping_data.append(latency)
            self.timestamps.append(current_time)

            # 保持最近120秒的数据（基于时间清理旧数据）
            cutoff_time = current_time - 120  # 保留最近120秒的数据

            # 找到需要保留的数据的起始索引
            keep_from_index = 0
            for i, timestamp in enumerate(self.timestamps):
                if timestamp >= cutoff_time:
                    keep_from_index = i
                    break

            # 清理超过120秒的旧数据
            if keep_from_index > 0:
                self.ping_data = self.ping_data[keep_from_index:]
                self.timestamps = self.timestamps[keep_from_index:]

            # 批量更新图表 - 减少重绘次数
            self.update_chart_display()

            # 动态调整Y轴范围
            self.update_y_range()
        else:
            # ping失败时也要添加数据点，保持时间连续性
            # 使用上一个有效值或0
            last_valid_latency = self.ping_data[-1] if self.ping_data else 0
            self.ping_data.append(last_valid_latency)
            self.timestamps.append(current_time)

            # 更新图表显示
            self.update_chart_display()
            
    def update_chart_display(self):
        """批量更新图表显示，减少重绘次数"""
        if not self.ping_data:
            return

        # 更新主曲线
        self.curve.setData(self.timestamps, self.ping_data)

        # 更新填充区域
        self.fill_curve.setData(self.timestamps, self.ping_data)

        # 更新阈值线（只在有数据时）
        if len(self.timestamps) > 1:
            self.threshold_line_120.setData([self.timestamps[0], self.timestamps[-1]], [120, 120])
            self.threshold_line_150.setData([self.timestamps[0], self.timestamps[-1]], [150, 150])

        # 自动调整X轴范围，确保始终显示最新数据
        if len(self.timestamps) > 1:
            # 使用基于时间的窗口，显示最近60秒的数据
            current_time = self.timestamps[-1]
            time_window = 60  # 显示最近60秒的数据

            if current_time <= time_window:
                # 程序运行时间少于60秒，显示全部
                x_min = max(0, self.timestamps[0] - 5)  # 左边留5秒边距
                x_max = current_time + 5  # 右边留5秒边距
            else:
                # 程序运行超过60秒，显示最近60秒的滚动窗口
                x_min = current_time - time_window
                x_max = current_time + 5  # 右边留5秒边距

            # 设置X轴范围，确保图表实时滚动
            self.graph_widget.setXRange(x_min, x_max, padding=0)
            
    def update_y_range(self):
        """更新Y轴范围"""
        if not self.ping_data or not self.auto_y_range:
            return
            
        # 获取当前数据的最大值
        max_value = max(self.ping_data) if self.ping_data else 50
        
        # 根据最大值动态调整Y轴范围
        if max_value < 50:
            self.y_range_max = 50  # 最小范围
        elif max_value < 100:
            self.y_range_max = 100
        elif max_value < 150:
            self.y_range_max = 150
        elif max_value < 200:
            self.y_range_max = 200
        else:
            self.y_range_max = max_value + 50  # 为最大值添加一些边距
            
        # 设置Y轴范围
        self.graph_widget.setYRange(self.y_range_min, self.y_range_max)
        
    def set_manual_y_range(self, max_value):
        """设置手动Y轴范围"""
        self.auto_y_range = False
        self.manual_y_max = max_value
        self.graph_widget.setYRange(self.y_range_min, self.manual_y_max)
        
    def set_auto_y_range(self, auto):
        """设置是否自动调整Y轴范围"""
        self.auto_y_range = auto
        if auto:
            self.update_y_range()
        else:
            self.graph_widget.setYRange(self.y_range_min, self.manual_y_max)