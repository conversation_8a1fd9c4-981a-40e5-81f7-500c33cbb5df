# 康多远程手术机器人监控系统 - 语言切换功能

## 🌐 多语言支持

### 支持的语言
- **中文 (zh)**: 默认语言，完整的中文界面
- **English (en)**: 英文界面，适合国际用户

### 功能特点
- **实时切换**: 无需重启程序，即时生效
- **配置保存**: 语言设置自动保存到配置文件
- **全面覆盖**: 所有界面文本都支持多语言

## 📋 界面文本对照

### 主要界面元素

| 中文 | English |
|------|---------|
| 康多远程手术机器人监控系统 | Kando Remote Surgery Robot Monitoring System |
| 网络监控 | Network Monitor |
| 系统配置 | System Config |
| 图表设置 | Chart Settings |
| 语言设置 | Language |

### 基本信息

| 中文 | English |
|------|---------|
| 基本信息 | Basic Information |
| 术者位置 | Surgeon Location |
| 患者位置 | Patient Location |
| 物理距离 | Physical Distance |
| 通讯距离 | Network Distance |
| 远程IP | Remote IP |

### 网络状态

| 中文 | English |
|------|---------|
| 网络状态 | Network Status |
| 当前延迟 | Current Latency |
| 丢包率 | Packet Loss |
| 连接状态 | Connection Status |
| 网络延迟历史 | Network Latency History |

### 状态描述

| 中文 | English |
|------|---------|
| 优秀 | Excellent |
| 良好 | Good |
| 较差 | Poor |
| 超时 | Timeout |
| 连接失败 | Connection Failed |
| 检测中 | Detecting |

### 网络类型

| 中文 | English |
|------|---------|
| 本地网络 | Local Network |
| 城域网络 | Metro Network |
| 区域网络 | Regional Network |
| 国内网络 | Domestic Network |
| 国际网络 | International Network |
| 网络异常 | Network Error |

### 图表元素

| 中文 | English |
|------|---------|
| 延迟 (ms) | Latency (ms) |
| 时间 (秒) | Time (s) |
| 实时延迟 | Real-time Latency |
| 警告阈值 | Warning Threshold |
| 危险阈值 | Danger Threshold |
| 范围 | Range |
| 手动范围 | Manual Range |

### 设置选项

| 中文 | English |
|------|---------|
| 保存配置 | Save Config |
| Y轴范围设置 | Y-Axis Range Settings |
| 自动调整Y轴范围 | Auto Adjust Y-Axis Range |
| 手动设置最大值 | Manual Max Value |
| 应用设置 | Apply Settings |
| 语言设置 | Language Settings |
| 选择语言 | Select Language |

## 🔧 使用方法

### 切换语言
1. 点击 **"语言设置"** / **"Language"** 选项卡
2. 选择所需语言：
   - **中文**: 选择 "中文" 单选按钮
   - **English**: 选择 "English" 单选按钮
3. 点击 **"应用设置"** / **"Apply Settings"** 按钮
4. 界面立即切换到所选语言

### 配置保存
- 语言设置自动保存到 `config.json` 文件
- 下次启动程序时会记住上次的语言选择
- 配置文件使用UTF-8编码，支持中文字符

## 💻 技术实现

### 语言包结构
```python
self.languages = {
    'zh': {
        'window_title': '康多远程手术机器人监控系统',
        'tab_monitoring': '网络监控',
        # ... 更多中文文本
    },
    'en': {
        'window_title': 'Kando Remote Surgery Robot Monitoring System',
        'tab_monitoring': 'Network Monitor',
        # ... 更多英文文本
    }
}
```

### 文本获取方法
```python
def get_text(self, key):
    """获取当前语言的文本"""
    return self.languages[self.current_language].get(key, key)
```

### 实时更新机制
```python
def update_all_texts(self):
    """更新所有界面文本"""
    # 更新窗口标题
    self.setWindowTitle(self.get_text('window_title'))
    
    # 更新选项卡标题
    self.tabs.setTabText(self.monitoring_tab_index, self.get_text('tab_monitoring'))
    
    # 更新所有标签文本
    # ...
```

## 🎯 设计特点

### 用户友好
- **直观操作**: 简单的单选按钮选择
- **即时反馈**: 点击应用后立即生效
- **视觉一致**: 两种语言保持相同的界面布局

### 国际化标准
- **UTF-8编码**: 完整支持中文和英文字符
- **标准术语**: 使用医疗和网络领域的标准术语
- **专业表达**: 符合国际医疗设备的表达习惯

### 扩展性
- **模块化设计**: 易于添加新语言
- **统一接口**: 所有文本通过统一方法获取
- **配置驱动**: 语言包独立于界面逻辑

## 🚀 使用场景

### 国内使用
- **中文界面**: 适合国内医院和医生使用
- **本土化术语**: 使用符合国内习惯的医疗术语

### 国际合作
- **英文界面**: 适合国际医疗合作项目
- **标准术语**: 使用国际通用的医疗和技术术语
- **跨国手术**: 支持跨国远程手术监控

这个多语言功能让监控系统能够服务于更广泛的用户群体，提升了系统的国际化水平和用户体验。
