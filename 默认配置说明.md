# 默认配置说明

## 📋 默认配置的作用

### 🎯 **主要功能**
默认配置在以下情况下发挥重要作用：

1. **首次启动**: 当用户第一次运行程序时，没有config.json文件
2. **配置丢失**: 当config.json文件被删除或损坏时
3. **快速演示**: 提供有意义的示例数据，便于演示和测试
4. **用户指导**: 显示配置项的正确格式和示例值

### 📁 **配置文件机制**

#### 加载顺序
```python
def load_config(self):
    try:
        # 1. 尝试读取现有配置文件
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 2. 文件不存在时使用默认配置
        return {
            "surgeon_location": "北京协和医院",
            "patient_location": "上海瑞金医院", 
            "physical_distance": "1200 km",
            "network_distance": "1500 km",
            "remote_ip": "***********",
            "language": "zh"
        }
```

#### 保存机制
```python
def save_config(self):
    # 用户修改配置后，自动保存到config.json
    config = {
        "surgeon_location": self.surgeon_location_edit.text(),
        "patient_location": self.patient_location_edit.text(),
        "physical_distance": self.physical_distance_edit.text(),
        "network_distance": self.network_distance_edit.text(),
        "remote_ip": self.remote_ip_edit.text(),
        "language": self.current_language
    }
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
```

## 🏥 **默认配置详解**

### 医疗场景设置
```json
{
    "surgeon_location": "北京协和医院",     // 术者位置 - 知名三甲医院
    "patient_location": "上海瑞金医院",    // 患者位置 - 知名三甲医院
    "physical_distance": "1200 km",       // 物理距离 - 北京到上海实际距离
    "network_distance": "1500 km",        // 网络距离 - 考虑网络路由
    "remote_ip": "***********",          // 远程IP - 常见网关地址
    "language": "zh"                       // 默认语言 - 中文
}
```

### 配置项说明

#### 1. **术者位置 (surgeon_location)**
- **作用**: 显示主刀医生所在医院
- **默认值**: "北京协和医院"
- **用途**: 界面显示、日志记录、报告生成

#### 2. **患者位置 (patient_location)**
- **作用**: 显示患者所在医院
- **默认值**: "上海瑞金医院"
- **用途**: 界面显示、距离计算参考

#### 3. **物理距离 (physical_distance)**
- **作用**: 两地实际地理距离
- **默认值**: "1200 km"
- **用途**: 延迟预估、网络质量评估

#### 4. **网络距离 (network_distance)**
- **作用**: 网络数据传输路径距离
- **默认值**: "1500 km"
- **用途**: 智能Y轴范围调整、延迟预期计算

#### 5. **远程IP (remote_ip)**
- **作用**: 网络延迟监控的目标地址
- **默认值**: "***********"
- **用途**: ping测试目标、网络质量监控

#### 6. **语言设置 (language)**
- **作用**: 界面显示语言
- **默认值**: "zh" (中文)
- **用途**: 多语言界面切换

## 🔧 **实际使用场景**

### 场景1: 首次部署
```
用户首次安装程序 → 没有config.json → 使用默认配置 → 
显示示例医院信息 → 用户根据实际情况修改 → 保存新配置
```

### 场景2: 演示展示
```
销售演示 → 使用默认配置 → 显示北京-上海远程手术场景 → 
实时网络监控 → 展示系统功能
```

### 场景3: 快速测试
```
开发测试 → 删除config.json → 重启程序 → 
自动加载默认配置 → 立即可用的测试环境
```

### 场景4: 配置恢复
```
配置文件损坏 → 程序启动失败 → 删除损坏文件 → 
重启程序 → 恢复到默认配置 → 重新设置
```

## 🎯 **优化建议**

### 当前默认配置的优点
- ✅ **真实场景**: 使用知名医院，场景可信
- ✅ **合理距离**: 1200km符合跨城市远程手术
- ✅ **实用IP**: ***********是常见的网关地址
- ✅ **本土化**: 默认中文适合国内用户

### 可能的改进
- 🔄 **多套预设**: 提供国内、国际等不同场景的预设
- 🔄 **智能检测**: 自动检测本地网关作为默认IP
- 🔄 **向导模式**: 首次启动时引导用户配置

## 📊 **配置文件示例**

### 生成的config.json
```json
{
  "surgeon_location": "北京协和医院",
  "patient_location": "上海瑞金医院",
  "physical_distance": "1200 km",
  "network_distance": "1500 km", 
  "remote_ip": "***********",
  "language": "zh"
}
```

### 国际化配置示例
```json
{
  "surgeon_location": "Johns Hopkins Hospital",
  "patient_location": "Mayo Clinic",
  "physical_distance": "800 miles",
  "network_distance": "1000 miles",
  "remote_ip": "*******",
  "language": "en"
}
```

## 🚀 **总结**

默认配置是系统的重要组成部分，它：

1. **保证可用性**: 确保程序在任何情况下都能启动
2. **提供指导**: 向用户展示正确的配置格式
3. **便于演示**: 提供有意义的示例数据
4. **降低门槛**: 新用户无需复杂配置即可体验功能

这个默认配置设计合理，既实用又具有示范作用，是系统用户体验的重要保障。
