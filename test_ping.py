#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import platform
import re

def test_ping(host):
    """测试ping函数"""
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    count = '3'
    command = ['ping', param, count, host]
    
    # Windows下添加超时参数
    if platform.system().lower() == 'windows':
        command.extend(['-w', '3000'])
    
    try:
        # 使用gbk编码解码Windows ping输出，隐藏控制台窗口
        if platform.system().lower() == 'windows':
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
            startupinfo.wShowWindow = subprocess.SW_HIDE
            output = subprocess.check_output(command, encoding='gbk', timeout=5, 
                                           startupinfo=startupinfo)
        else:
            output = subprocess.check_output(command, timeout=5).decode('utf-8')

        print("=== Ping输出 ===")
        print(output)
        print("================")

        if platform.system().lower() == 'windows':
            # Windows系统下提取延迟和丢包率
            if 'TTL=' in output:
                print("找到TTL，开始提取延迟...")
                # 提取延迟值
                times = re.findall(r'时间=(\d+)ms', output)
                print(f"提取到的延迟值: {times}")
                
                if times:
                    # 获取最后一个延迟值
                    current_latency = float(times[-1])
                    # 计算平均延迟
                    if len(times) > 1:
                        avg_latency = sum(float(t) for t in times) / len(times)
                    else:
                        avg_latency = current_latency
                    
                    print(f"当前延迟: {current_latency}ms")
                    print(f"平均延迟: {avg_latency}ms")
                    
                    latency = (current_latency, avg_latency)
                else:
                    print("未找到延迟值")
                    latency = None

                # 提取丢包率
                if '已发送 = ' in output and '已接收 = ' in output:
                    sent_match = re.search(r'已发送 = (\d+)', output)
                    received_match = re.search(r'已接收 = (\d+)', output)

                    if sent_match and received_match:
                        sent = int(sent_match.group(1))
                        received = int(received_match.group(1))
                        loss = ((sent - received) / sent) * 100
                        print(f"丢包率: {loss}%")
                    else:
                        loss = 0
                        print("未找到丢包率信息，设为0%")
                else:
                    loss = 0
                    print("未找到丢包率信息，设为0%")

                return latency, loss
            else:
                print("未找到TTL，ping失败")
                return None, 100
        else:
            print("Linux/Mac系统处理...")
            # Linux/Mac处理逻辑
            return None, 100
            
    except subprocess.TimeoutExpired:
        print("Ping超时")
        return None, 100
    except Exception as e:
        print(f"Ping错误: {e}")
        return None, 100

if __name__ == "__main__":
    print("测试ping功能...")
    
    # 测试本地回环
    print("\n=== 测试 127.0.0.1 ===")
    result = test_ping("127.0.0.1")
    print(f"结果: {result}")
    
    # 测试Google DNS
    print("\n=== 测试 8.8.8.8 ===")
    result = test_ping("8.8.8.8")
    print(f"结果: {result}")
