import subprocess
import platform
import re

def ping_host(host):
    """执行ping命令并返回延迟和丢包率 - 与主程序相同的逻辑"""
    param = '-n' if platform.system().lower() == 'windows' else '-c'
    count = '5'  # 增加ping次数以获得更准确的统计
    command = ['ping', param, count, host]
    try:
        # 使用gbk编码解码Windows ping输出
        if platform.system().lower() == 'windows':
            output = subprocess.check_output(command, encoding='gbk')
        else:
            output = subprocess.check_output(command).decode('utf-8')
            
        if platform.system().lower() == 'windows':
            # Windows系统下提取延迟和丢包率
            if 'TTL=' in output:
                # 提取平均延迟 - 修正解析逻辑
                if '平均 = ' in output:
                    avg_line = output.split('平均 = ')[1].split('ms')[0].strip()
                    latency = float(avg_line)
                else:
                    # 如果没有统计信息，从最后一个回复中提取延迟
                    times = re.findall(r'时间=(\d+)ms', output)
                    if times:
                        latency = float(times[-1])  # 使用最后一个延迟值
                    else:
                        latency = 0
                
                # 提取丢包率 - 修正解析逻辑
                if '已发送 = ' in output and '已接收 = ' in output:
                    # 提取发送和接收的数据包数量
                    sent_match = re.search(r'已发送 = (\d+)', output)
                    received_match = re.search(r'已接收 = (\d+)', output)
                    
                    if sent_match and received_match:
                        sent = int(sent_match.group(1))
                        received = int(received_match.group(1))
                        loss = ((sent - received) / sent) * 100
                    else:
                        loss = 0
                else:
                    loss = 0
                
                return latency, loss
            else:
                return None, 100
        else:
            # Linux/Mac系统下提取延迟和丢包率
            if 'received' in output:
                # 提取平均延迟
                if 'avg' in output:
                    avg_section = output.split('min/avg/max')[1].split('=')[1].strip()
                    latency = float(avg_section.split('/')[1])
                else:
                    latency = 0
                
                # 提取丢包率
                packet_stats = output.split(',')[1].strip()
                received = int(packet_stats.split(' ')[0])
                loss = 100 - (received / int(count) * 100)
                
                return latency, loss
            else:
                return None, 100
    except Exception as e:
        print(f"Ping error: {e}")  # 添加调试信息
        return None, 100

def test_multiple_hosts():
    """测试多个主机的ping功能"""
    hosts = ["***********", "*******", "*************"]  # 包括一个可能不存在的主机
    
    for host in hosts:
        print(f"\n测试ping {host}...")
        latency, loss = ping_host(host)
        
        if latency is not None:
            print(f"✓ 成功: 延迟={latency:.1f}ms, 丢包率={loss:.1f}%")
            
            # 模拟主程序的状态判断逻辑
            if latency < 50:
                status = "优秀 (绿色)"
            elif latency < 100:
                status = "一般 (橙色)"
            else:
                status = "较差 (红色)"
            print(f"  延迟状态: {status}")
            
            if loss < 1:
                loss_status = "优秀 (绿色)"
            elif loss < 5:
                loss_status = "一般 (橙色)"
            else:
                loss_status = "较差 (红色)"
            print(f"  丢包状态: {loss_status}")
        else:
            print(f"✗ 失败: 超时或无法访问")

if __name__ == "__main__":
    print("=== 网络延迟和丢包率测试 ===")
    test_multiple_hosts()
    print("\n测试完成！")
