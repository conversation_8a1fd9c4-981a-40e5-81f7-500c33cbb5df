#!/usr/bin/env python
# -*- coding: utf-8 -*-

import subprocess
import platform
import re
from PyQt5.QtCore import QRunnable, pyqtSignal, QObject

class PingWorkerSignals(QObject):
    """Ping工作线程的信号类"""
    finished = pyqtSignal(object, float)  # latency_data (tuple or number), loss
    error = pyqtSignal()

class PingWorker(QRunnable):
    """异步执行ping命令的工作线程"""
    def __init__(self, host):
        super().__init__()
        self.host = host
        self.signals = PingWorkerSignals()

    def run(self):
        """在后台线程中执行ping"""
        try:
            latency_data, loss = self.ping_host_sync(self.host)
            # latency_data现在是(current_latency, avg_latency)元组或None
            if latency_data is not None:
                self.signals.finished.emit(latency_data, loss)
            else:
                self.signals.finished.emit(-1, loss)
        except RuntimeError:
            # 信号对象已被删除，静默忽略
            pass
        except Exception as e:
            print(f"Ping worker error: {e}")
            try:
                self.signals.error.emit()
            except RuntimeError:
                # 信号对象已被删除，静默忽略
                pass

    def ping_host_sync(self, host):
        """同步ping函数（在工作线程中调用）"""
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        count = '3'  # 使用3次ping获取多个延迟值用于计算平均
        command = ['ping', param, count, host]

        # Windows下添加超时参数
        if platform.system().lower() == 'windows':
            command.extend(['-w', '3000'])  # 3秒超时
        try:
            # 使用gbk编码解码Windows ping输出，设置较短超时，隐藏控制台窗口
            if platform.system().lower() == 'windows':
                # Windows下隐藏控制台窗口
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                output = subprocess.check_output(command, encoding='gbk', timeout=5,
                                               startupinfo=startupinfo)
            else:
                output = subprocess.check_output(command, timeout=5).decode('utf-8')

            if platform.system().lower() == 'windows':
                # Windows系统下提取延迟和丢包率
                if 'TTL=' in output:
                    # 提取延迟值，支持多种格式：时间=50ms, 时间<1ms
                    times = re.findall(r'时间[=<](\d+)ms', output)
                    # 如果没找到，尝试提取 时间<1ms 这种情况，设为0.5ms
                    if not times:
                        if '时间<1ms' in output:
                            times = ['0.5'] * output.count('时间<1ms')
                        else:
                            times = ['0.5']
                    if times:
                        # 获取最后一个延迟值
                        current_latency = float(times[-1])
                        # 计算平均延迟（如果有多个值）
                        if len(times) > 1:
                            avg_latency = sum(float(t) for t in times) / len(times)
                        else:
                            avg_latency = current_latency
                        # 返回当前延迟和平均延迟的元组
                        latency = (current_latency, avg_latency)
                    else:
                        latency = None

                    # 提取丢包率
                    if '已发送 = ' in output and '已接收 = ' in output:
                        # 提取发送和接收的数据包数量
                        sent_match = re.search(r'已发送 = (\d+)', output)
                        received_match = re.search(r'已接收 = (\d+)', output)

                        if sent_match and received_match:
                            sent = int(sent_match.group(1))
                            received = int(received_match.group(1))
                            loss = ((sent - received) / sent) * 100
                        else:
                            loss = 0
                    else:
                        loss = 0

                    return latency, loss
                else:
                    return None, 100
            else:
                # Linux/Mac系统下提取延迟和丢包率
                if 'received' in output:
                    # 提取所有延迟值
                    times = re.findall(r'time=(\d+\.?\d*)ms', output)
                    if times:
                        # 转换为浮点数列表
                        latency_values = [float(t) for t in times]
                        # 计算平均延迟
                        avg_latency = sum(latency_values) / len(latency_values)
                        # 获取最新延迟（最后一个值）
                        current_latency = latency_values[-1]
                        # 返回当前延迟和平均延迟的元组
                        latency = (current_latency, avg_latency)
                    else:
                        latency = None

                    # 提取丢包率
                    packet_stats = output.split(',')[1].strip()
                    received = int(packet_stats.split(' ')[0])
                    loss = 100 - (received / int(count) * 100)

                    return latency, loss
                else:
                    return None, 100
        except subprocess.TimeoutExpired:
            return None, 100
        except Exception as e:
            print(f"Ping error: {e}")
            return None, 100