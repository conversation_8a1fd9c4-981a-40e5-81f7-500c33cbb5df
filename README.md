# 康多远程手术机器人监控系统

## 项目结构

```
项目目录/
├── main.py                     # 主程序入口
├── network/                    # 网络相关模块
│   └── ping_worker.py          # Ping工作线程
├── ui/                         # 用户界面模块
│   ├── main_window.py          # 主窗口类
│   └── chart_manager.py        # 图表管理类
├── utils/                      # 工具模块
│   ├── config_manager.py       # 配置管理
│   └── language_manager.py     # 语言管理
├── config.json                
```

## 安装依赖

```bash
pip install PyQt5 pyqtgraph numpy
```

## 运行程序

```bash
python remote_surgery_interface.py
```

## 功能特点

- 显示术者和患者位置信息
- 显示物理距离和通讯距离
- 实时监控网络延迟和丢包率
- 图形化展示网络延迟历史数据
- 可通过配置页面修改相关信息
