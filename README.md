# 康多远程手术机器人监控系统

## 项目结构

```
项目目录/
├── main.py                     # 主程序入口
├── network/                    # 网络相关模块
│   └── ping_worker.py          # Ping工作线程
├── ui/                         # 用户界面模块
│   ├── main_window.py          # 主窗口类
│   └── chart_manager.py        # 图表管理类
├── utils/                      # 工具模块
│   ├── config_manager.py       # 配置管理
│   └── language_manager.py     # 语言管理
├── config.json                 # 配置文件
├── build.bat                   # 构建脚本
├── build_no_console.bat        # 无控制台构建脚本
└── *.spec                      # PyInstaller配置文件
```

## 模块说明

### 主程序 (main.py)
- 程序入口点
- 初始化QApplication
- 启动主窗口

### 网络模块 (network/)
- **ping_worker.py**: 异步Ping工作线程，支持Windows/Linux跨平台

### 界面模块 (ui/)
- **main_window.py**: 主窗口类，包含所有UI逻辑和网络状态管理
- **chart_manager.py**: 图表管理类，负责延迟数据的可视化

### 工具模块 (utils/)
- **config_manager.py**: 配置文件的加载和保存
- **language_manager.py**: 多语言支持（中文/英文）

## 安装依赖

```bash
pip install PyQt5 pyqtgraph numpy
```

## 运行程序

```bash
python main.py
```

## 功能特点

- 显示术者和患者位置信息
- 显示物理距离和通讯距离
- 实时监控网络延迟和丢包率
- 图形化展示网络延迟历史数据
- 可通过配置页面修改相关信息
- 多语言支持（中文/英文）
- 网络状态颜色指示：
  - 🟢 绿色：延迟 < 120ms（优秀）
  - 🟡 黄色：延迟 120-200ms（良好）
  - 🔴 红色：延迟 ≥ 200ms 或超时（较差）

## 网络状态颜色说明

网络状态颜色阈值设置：
- **120ms以下**：绿色显示，表示网络状态优秀
- **120-200ms**：黄色显示，表示网络状态良好
- **200ms以上或超时**：红色显示，表示网络状态较差
