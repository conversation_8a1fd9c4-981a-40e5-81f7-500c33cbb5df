#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        # 默认配置 - 提供实用的示例配置
        return {
            "surgeon_location": "北京协和医院",
            "patient_location": "上海瑞金医院",
            "physical_distance": "1200 km",
            "network_distance": "1500 km",
            "remote_ip": "***********",  # 更常见的网关地址
            "language": "zh"
        }

def save_config(config):
    """保存配置文件"""
    with open('config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)