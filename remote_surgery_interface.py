import sys
import time
import random
import subprocess
import platform
import re
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton,
                            QTabWidget, QGridLayout, QGroupBox, QFormLayout)
from PyQt5.QtCore import QTimer, Qt, pyqtSlot, QRunnable, pyqtSignal, QObject
from PyQt5.QtGui import QFont, QColor
import pyqtgraph as pg
import numpy as np
import json

class PingWorkerSignals(QObject):
    """Ping工作线程的信号类"""
    finished = pyqtSignal(object, float)  # latency_data (tuple or number), loss
    error = pyqtSignal()

class PingWorker(QRunnable):
    """异步执行ping命令的工作线程"""
    def __init__(self, host):
        super().__init__()
        self.host = host
        self.signals = PingWorkerSignals()

    def run(self):
        """在后台线程中执行ping"""
        try:
            latency_data, loss = self.ping_host_sync(self.host)
            # latency_data现在是(current_latency, avg_latency)元组或None
            if latency_data is not None:
                self.signals.finished.emit(latency_data, loss)
            else:
                self.signals.finished.emit(-1, loss)
        except Exception as e:
            print(f"Ping worker error: {e}")
            self.signals.error.emit()

    def ping_host_sync(self, host):
        """同步ping函数（在工作线程中调用）"""
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        count = '3'  # 使用3次ping获取多个延迟值用于计算平均
        command = ['ping', param, count, host]

        # Windows下添加超时参数
        if platform.system().lower() == 'windows':
            command.extend(['-w', '3000'])  # 3秒超时
        try:
            # 使用gbk编码解码Windows ping输出，设置较短超时，隐藏控制台窗口
            if platform.system().lower() == 'windows':
                # Windows下隐藏控制台窗口
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                output = subprocess.check_output(command, encoding='gbk', timeout=5,
                                               startupinfo=startupinfo)
            else:
                output = subprocess.check_output(command, timeout=5).decode('utf-8')

            if platform.system().lower() == 'windows':
                # Windows系统下提取延迟和丢包率
                if 'TTL=' in output:
                    # 提取延迟值，支持多种格式：时间=50ms, 时间<1ms
                    times = re.findall(r'时间[=<](\d+)ms', output)
                    # 如果没找到，尝试提取 时间<1ms 这种情况，设为0.5ms
                    if not times:
                        if '时间<1ms' in output:
                            times = ['0.5'] * output.count('时间<1ms')
                        else:
                            times = ['0.5']
                    if times:
                        # 获取最后一个延迟值
                        current_latency = float(times[-1])
                        # 计算平均延迟（如果有多个值）
                        if len(times) > 1:
                            avg_latency = sum(float(t) for t in times) / len(times)
                        else:
                            avg_latency = current_latency
                        # 返回当前延迟和平均延迟的元组
                        latency = (current_latency, avg_latency)
                    else:
                        latency = None

                    # 提取丢包率
                    if '已发送 = ' in output and '已接收 = ' in output:
                        # 提取发送和接收的数据包数量
                        sent_match = re.search(r'已发送 = (\d+)', output)
                        received_match = re.search(r'已接收 = (\d+)', output)

                        if sent_match and received_match:
                            sent = int(sent_match.group(1))
                            received = int(received_match.group(1))
                            loss = ((sent - received) / sent) * 100
                        else:
                            loss = 0
                    else:
                        loss = 0

                    return latency, loss
                else:
                    return None, 100
            else:
                # Linux/Mac系统下提取延迟和丢包率
                if 'received' in output:
                    # 提取所有延迟值
                    times = re.findall(r'time=(\d+\.?\d*)ms', output)
                    if times:
                        # 转换为浮点数列表
                        latency_values = [float(t) for t in times]
                        # 计算平均延迟
                        avg_latency = sum(latency_values) / len(latency_values)
                        # 获取最新延迟（最后一个值）
                        current_latency = latency_values[-1]
                        # 返回当前延迟和平均延迟的元组
                        latency = (current_latency, avg_latency)
                    else:
                        latency = None

                    # 提取丢包率
                    packet_stats = output.split(',')[1].strip()
                    received = int(packet_stats.split(' ')[0])
                    loss = 100 - (received / int(count) * 100)

                    return latency, loss
                else:
                    return None, 100
        except subprocess.TimeoutExpired:
            return None, 100
        except Exception as e:
            print(f"Ping error: {e}")
            return None, 100

class RemoteSurgeryInterface(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("康多远程手术机器人监控系统")
        self.setMinimumSize(1200, 800)

        # 设置主窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 10px;
                background-color: #ffffff;
                margin-top: 5px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 10pt;
                font-weight: bold;
                min-width: 140px;
                max-width: 180px;
                min-height: 40px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)
        
        # 初始化数据
        self.config = self.load_config()
        self.ping_data = []
        self.timestamps = []
        self.packet_loss = 0
        self.y_range_min = 0
        self.y_range_max = 50  # 初始范围
        self.auto_y_range = True  # 是否自动调整Y轴范围
        self.manual_y_max = 200  # 手动设置的Y轴最大值
        self.current_language = self.config.get('language', 'zh')  # 默认中文

        # 网络连接状态管理
        self.network_connected = False
        self.successful_pings = 0
        self.total_ping_attempts = 0

        # 时间管理
        self.start_time = time.time()  # 记录程序启动时间

        # 初始化语言包
        self.init_languages()
        
        # 创建主界面
        self.init_ui()
        
        # 设置定时器更新数据 - 优化性能
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(500)  # 每0.5秒检查一次，但图表按固定间隔更新

        # 初始化线程池用于异步ping
        from PyQt5.QtCore import QThreadPool
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(1)  # 限制为单线程避免并发问题

        # ping状态管理
        self.ping_in_progress = False
        self.last_ping_result = (None, 100)  # 缓存上次结果

        # 数据刷新管理
        self.last_update_time = 0  # 上次更新图表的时间
        self.update_interval = 3  # 固定3秒更新间隔
        self.pending_data = []  # 待更新的数据队列

    def init_languages(self):
        """初始化语言包"""
        self.languages = {
            'zh': {
                'window_title': '康多远程手术机器人监控系统',
                'tab_monitoring': '网络监控',
                'tab_config': '系统配置',
                'tab_chart': '图表设置',
                'tab_language': '语言设置',
                'basic_info': '基本信息',
                'surgeon_location': '术者位置',
                'patient_location': '患者位置',
                'physical_distance': '物理距离',
                'network_distance': '通讯距离',
                'remote_ip': '远程IP',
                'network_status': '网络状态',
                'current_latency': '当前延迟',
                'average_latency': '平均延迟',
                'packet_loss': '丢包率',
                'connection_status': '连接状态',
                'network_history': '网络延迟历史',
                'latency_ms': '延迟 (ms)',
                'time_s': '时间 (秒)',
                'save_config': '保存配置',
                'chart_settings': 'Y轴范围设置',
                'auto_range': '自动调整Y轴范围',
                'manual_max': '手动设置最大值',
                'apply_settings': '应用设置',
                'language_settings': '语言设置',
                'select_language': '选择语言',
                'chinese': '中文',
                'english': 'English',
                'timeout': '超时',
                'excellent': '优秀',
                'good': '良好',
                'poor': '较差',
                'failed': '连接失败',
                'detecting': '检测中',
                'local_network': '本地网络',
                'metro_network': '城域网络',
                'regional_network': '区域网络',
                'domestic_network': '国内网络',
                'international_network': '国际网络',
                'network_error': '网络异常',
                'real_time_latency': '实时延迟',
                'warning_threshold': '警告阈值',
                'danger_threshold': '危险阈值',
                'range': '范围',
                'manual_range': '手动范围'
            },
            'en': {
                'window_title': 'Kando Remote Surgery Robot Monitoring System',
                'tab_monitoring': 'Monitor',
                'tab_config': 'Config',
                'tab_chart': 'Chart',
                'tab_language': 'Language',
                'basic_info': 'Basic Information',
                'surgeon_location': 'Surgeon Location',
                'patient_location': 'Patient Location',
                'physical_distance': 'Physical Distance',
                'network_distance': 'Network Distance',
                'remote_ip': 'Remote IP',
                'network_status': 'Network Status',
                'current_latency': 'Current Latency',
                'average_latency': 'Average Latency',
                'packet_loss': 'Packet Loss',
                'connection_status': 'Connection Status',
                'network_history': 'Network Latency History',
                'latency_ms': 'Latency (ms)',
                'time_s': 'Time (s)',
                'save_config': 'Save Config',
                'chart_settings': 'Y-Axis Range Settings',
                'auto_range': 'Auto Adjust Y-Axis Range',
                'manual_max': 'Manual Max Value',
                'apply_settings': 'Apply Settings',
                'language_settings': 'Language Settings',
                'select_language': 'Select Language',
                'chinese': '中文',
                'english': 'English',
                'timeout': 'Timeout',
                'excellent': 'Excellent',
                'good': 'Good',
                'poor': 'Poor',
                'failed': 'Connection Failed',
                'detecting': 'Detecting',
                'local_network': 'Local Network',
                'metro_network': 'Metro Network',
                'regional_network': 'Regional Network',
                'domestic_network': 'Domestic Network',
                'international_network': 'International Network',
                'network_error': 'Network Error',
                'real_time_latency': 'Real-time Latency',
                'warning_threshold': 'Warning Threshold',
                'danger_threshold': 'Danger Threshold',
                'range': 'Range',
                'manual_range': 'Manual Range'
            }
        }

    def get_text(self, key):
        """获取当前语言的文本"""
        return self.languages[self.current_language].get(key, key)
        
    def load_config(self):
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            # 默认配置 - 提供实用的示例配置
            return {
                "surgeon_location": "北京协和医院",
                "patient_location": "上海瑞金医院",
                "physical_distance": "1200 km",
                "network_distance": "1500 km",
                "remote_ip": "***********",  # 更常见的网关地址
                "language": "zh"
            }
    
    def save_config(self):
        config = {
            "surgeon_location": self.surgeon_location_edit.text(),
            "patient_location": self.patient_location_edit.text(),
            "physical_distance": self.physical_distance_edit.text(),
            "network_distance": self.network_distance_edit.text(),
            "remote_ip": self.remote_ip_edit.text(),
            "language": self.current_language
        }
        with open('config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        self.config = config
        self.update_info_display()
    
    def init_ui(self):
        # 创建中央部件和主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        tabs = QTabWidget()
        main_layout.addWidget(tabs)
        
        # 创建监控界面
        monitoring_tab = QWidget()
        self.monitoring_tab_index = tabs.addTab(monitoring_tab, self.get_text('tab_monitoring'))

        # 创建配置界面
        config_tab = QWidget()
        self.config_tab_index = tabs.addTab(config_tab, self.get_text('tab_config'))

        # 创建图表设置界面
        chart_config_tab = QWidget()
        self.chart_tab_index = tabs.addTab(chart_config_tab, self.get_text('tab_chart'))

        # 创建语言设置界面
        language_tab = QWidget()
        self.language_tab_index = tabs.addTab(language_tab, self.get_text('tab_language'))

        # 设置各个界面
        self.setup_monitoring_tab(monitoring_tab)
        self.setup_config_tab(config_tab)
        self.setup_chart_config_tab(chart_config_tab)
        self.setup_language_tab(language_tab)

        # 保存tabs引用以便更新
        self.tabs = tabs
        
        self.setCentralWidget(central_widget)
    
    def setup_monitoring_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        # 信息显示区域
        self.info_group = QGroupBox(self.get_text('basic_info'))
        self.info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        info_layout = QGridLayout()
        
        # 术者和患者信息
        self.surgeon_label = QLabel(f"{self.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_label = QLabel(f"{self.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label = QLabel(f"{self.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label = QLabel(f"{self.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label = QLabel(f"{self.get_text('remote_ip')}: {self.config['remote_ip']}")
        
        # 设置字体 - 增大字体
        font = QFont()
        font.setPointSize(14)
        font.setWeight(QFont.Medium)
        self.surgeon_label.setFont(font)
        self.patient_label.setFont(font)
        self.physical_distance_label.setFont(font)
        self.network_distance_label.setFont(font)
        self.remote_ip_label.setFont(font)

        # 设置标签样式
        label_style = """
            QLabel {
                color: #2c3e50;
                background-color: #ecf0f1;
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #bdc3c7;
            }
        """
        self.surgeon_label.setStyleSheet(label_style)
        self.patient_label.setStyleSheet(label_style)
        self.physical_distance_label.setStyleSheet(label_style)
        self.network_distance_label.setStyleSheet(label_style)
        self.remote_ip_label.setStyleSheet(label_style)
        
        info_layout.addWidget(self.surgeon_label, 0, 0)
        info_layout.addWidget(self.patient_label, 0, 1)
        info_layout.addWidget(self.physical_distance_label, 1, 0)
        info_layout.addWidget(self.network_distance_label, 1, 1)
        info_layout.addWidget(self.remote_ip_label, 2, 0, 1, 2)
        
        self.info_group.setLayout(info_layout)
        layout.addWidget(self.info_group)
        
        # 网络状态显示区域
        self.network_group = QGroupBox(self.get_text('network_status'))
        self.network_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #27ae60;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        network_layout = QHBoxLayout()
        
        # 延迟和丢包率显示
        self.latency_label = QLabel(f"{self.get_text('current_latency')}: -- ms")
        self.avg_latency_label = QLabel(f"{self.get_text('average_latency')}: -- ms")
        self.packet_loss_label = QLabel(f"{self.get_text('packet_loss')}: -- %")

        # 添加连接状态指示器
        self.status_indicator = QLabel(f"● {self.get_text('connection_status')}: {self.get_text('detecting')}...")
        status_font = QFont()
        status_font.setPointSize(12)  # 减小连接状态指示器字体
        status_font.setWeight(QFont.Bold)
        self.status_indicator.setFont(status_font)
        self.status_indicator.setStyleSheet("""
            QLabel {
                color: #f39c12;
                background-color: #fef9e7;
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #f39c12;
            }
        """)

        # 设置合适的字体和美观样式 - 减小字体以适应英文显示
        status_font = QFont()
        status_font.setPointSize(14)  # 从16pt减小到14pt
        status_font.setWeight(QFont.Bold)
        self.latency_label.setFont(status_font)
        self.avg_latency_label.setFont(status_font)
        self.packet_loss_label.setFont(status_font)

        # 设置状态标签样式
        status_style = """
            QLabel {
                color: #27ae60;
                background-color: #d5f4e6;
                padding: 12px;
                border-radius: 8px;
                border: 2px solid #27ae60;
                font-weight: bold;
                text-align: center;
            }
        """
        self.latency_label.setStyleSheet(status_style)
        self.packet_loss_label.setStyleSheet(status_style)
        
        # 创建网络状态布局
        status_row1 = QHBoxLayout()
        status_row1.addWidget(self.latency_label)
        status_row1.addWidget(self.avg_latency_label)
        status_row1.addWidget(self.packet_loss_label)

        status_row2 = QHBoxLayout()
        status_row2.addWidget(self.status_indicator)
        status_row2.addStretch()

        network_layout.addLayout(status_row1)
        network_layout.addLayout(status_row2)
        
        self.network_group.setLayout(network_layout)
        layout.addWidget(self.network_group)
        
        # 图表显示区域
        self.chart_group = QGroupBox(self.get_text('network_history'))
        self.chart_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #9b59b6;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        chart_layout = QVBoxLayout()
        
        # 创建图表
        self.graph_widget = pg.PlotWidget()

        # 设置图表背景和样式
        self.graph_widget.setBackground('#f8f9fa')
        self.graph_widget.setTitle(self.get_text('network_history'), color="#2c3e50", size="16pt")

        # 设置坐标轴标签
        self.graph_widget.setLabel("left", self.get_text('latency_ms'), color="#2c3e50", size="14pt")
        self.graph_widget.setLabel("bottom", self.get_text('time_s'), color="#2c3e50", size="14pt")

        # 设置网格样式
        self.graph_widget.showGrid(x=True, y=True, alpha=0.3)
        self.graph_widget.getAxis('left').setPen(color='#34495e', width=2)
        self.graph_widget.getAxis('bottom').setPen(color='#34495e', width=2)
        self.graph_widget.getAxis('left').setTextPen(color='#2c3e50')
        self.graph_widget.getAxis('bottom').setTextPen(color='#2c3e50')

        # 设置Y轴范围
        self.graph_widget.setYRange(self.y_range_min, self.y_range_max)

        # 创建主曲线 - 使用渐变色
        gradient_pen = pg.mkPen(color='#3498db', width=3)
        self.curve = self.graph_widget.plot(pen=gradient_pen)

        # 添加填充区域
        self.fill_curve = self.graph_widget.plot(fillLevel=0,
                                                brush=pg.mkBrush(color=(52, 152, 219, 50)))

        # 添加阈值线
        self.threshold_line_120 = self.graph_widget.plot(pen=pg.mkPen(color='#f39c12', width=2, style=2))
        self.threshold_line_150 = self.graph_widget.plot(pen=pg.mkPen(color='#e74c3c', width=2, style=2))

        # 添加图例
        legend = self.graph_widget.addLegend(offset=(10, 10))
        legend.addItem(self.curve, self.get_text('real_time_latency'))
        legend.addItem(self.threshold_line_120, f"{self.get_text('warning_threshold')} (120ms)")
        legend.addItem(self.threshold_line_150, f"{self.get_text('danger_threshold')} (150ms)")

        # 设置图例样式
        legend.setLabelTextColor('#2c3e50')
        legend.setBrush(pg.mkBrush(255, 255, 255, 200))
        legend.setPen(pg.mkPen('#3498db', width=2))

        # 禁用鼠标交互功能，确保图表能实时更新
        self.graph_widget.setMouseEnabled(x=False, y=False)  # 禁用鼠标拖拽
        self.graph_widget.getViewBox().setMouseEnabled(x=False, y=False)  # 禁用ViewBox鼠标交互
        self.graph_widget.getViewBox().disableAutoRange()  # 禁用自动范围，我们手动控制

        # 禁用右键菜单
        self.graph_widget.getViewBox().setMenuEnabled(False)

        # 禁用滚轮缩放
        self.graph_widget.wheelEvent = lambda event: None
        
        chart_layout.addWidget(self.graph_widget)
        self.chart_group.setLayout(chart_layout)
        layout.addWidget(self.chart_group)
    
    def setup_config_tab(self, tab):
        layout = QVBoxLayout(tab)
        
        form_layout = QFormLayout()
        
        # 创建输入框
        self.surgeon_location_edit = QLineEdit(self.config["surgeon_location"])
        self.patient_location_edit = QLineEdit(self.config["patient_location"])
        self.physical_distance_edit = QLineEdit(self.config["physical_distance"])
        self.network_distance_edit = QLineEdit(self.config["network_distance"])
        self.remote_ip_edit = QLineEdit(self.config["remote_ip"])
        
        # 添加到表单
        self.config_form_layout = form_layout
        form_layout.addRow(f"{self.get_text('surgeon_location')}:", self.surgeon_location_edit)
        form_layout.addRow(f"{self.get_text('patient_location')}:", self.patient_location_edit)
        form_layout.addRow(f"{self.get_text('physical_distance')}:", self.physical_distance_edit)
        form_layout.addRow(f"{self.get_text('network_distance')}:", self.network_distance_edit)
        form_layout.addRow(f"{self.get_text('remote_ip')}:", self.remote_ip_edit)

        layout.addLayout(form_layout)

        # 保存按钮
        self.save_button = QPushButton(self.get_text('save_config'))
        self.save_button.clicked.connect(self.save_config)
        layout.addWidget(self.save_button)
        
        # 添加弹性空间
        layout.addStretch()
    
    def setup_chart_config_tab(self, tab):
        layout = QVBoxLayout(tab)

        # 图表设置组
        self.chart_config_group = QGroupBox(self.get_text('chart_settings'))
        chart_layout = QVBoxLayout()

        # 自动调整选项
        from PyQt5.QtWidgets import QCheckBox, QSpinBox
        self.auto_range_checkbox = QCheckBox(self.get_text('auto_range'))
        self.auto_range_checkbox.setChecked(self.auto_y_range)
        self.auto_range_checkbox.stateChanged.connect(self.on_auto_range_changed)
        chart_layout.addWidget(self.auto_range_checkbox)

        # 手动设置范围
        manual_layout = QHBoxLayout()
        self.manual_label = QLabel(f"{self.get_text('manual_max')}:")
        manual_layout.addWidget(self.manual_label)
        self.manual_y_spinbox = QSpinBox()
        self.manual_y_spinbox.setRange(50, 1000)
        self.manual_y_spinbox.setValue(self.manual_y_max)
        self.manual_y_spinbox.setSuffix(" ms")
        self.manual_y_spinbox.setEnabled(not self.auto_y_range)
        self.manual_y_spinbox.valueChanged.connect(self.on_manual_y_changed)
        manual_layout.addWidget(self.manual_y_spinbox)
        manual_layout.addStretch()
        chart_layout.addLayout(manual_layout)

        # 应用按钮
        self.apply_chart_button = QPushButton(self.get_text('apply_settings'))
        self.apply_chart_button.clicked.connect(self.apply_chart_settings)
        chart_layout.addWidget(self.apply_chart_button)

        self.chart_config_group.setLayout(chart_layout)
        layout.addWidget(self.chart_config_group)

        # 添加弹性空间
        layout.addStretch()

    def setup_language_tab(self, tab):
        """设置语言选择界面"""
        layout = QVBoxLayout(tab)

        # 语言设置组
        language_group = QGroupBox(self.get_text('language_settings'))
        language_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e67e22;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        language_layout = QVBoxLayout()

        # 语言选择
        from PyQt5.QtWidgets import QRadioButton, QButtonGroup
        self.language_group = QButtonGroup()

        # 中文选项
        self.chinese_radio = QRadioButton(self.get_text('chinese'))
        self.chinese_radio.setChecked(self.current_language == 'zh')
        self.chinese_radio.setStyleSheet("font-size: 14pt; padding: 5px;")
        self.language_group.addButton(self.chinese_radio, 0)
        language_layout.addWidget(self.chinese_radio)

        # 英文选项
        self.english_radio = QRadioButton(self.get_text('english'))
        self.english_radio.setChecked(self.current_language == 'en')
        self.english_radio.setStyleSheet("font-size: 14pt; padding: 5px;")
        self.language_group.addButton(self.english_radio, 1)
        language_layout.addWidget(self.english_radio)

        # 应用按钮
        apply_language_button = QPushButton(self.get_text('apply_settings'))
        apply_language_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d35400;
            }
        """)
        apply_language_button.clicked.connect(self.apply_language_change)
        language_layout.addWidget(apply_language_button)

        language_group.setLayout(language_layout)
        layout.addWidget(language_group)

        # 添加弹性空间
        layout.addStretch()

    def apply_language_change(self):
        """应用语言更改"""
        if self.chinese_radio.isChecked():
            new_language = 'zh'
        else:
            new_language = 'en'

        if new_language != self.current_language:
            self.current_language = new_language
            self.config['language'] = new_language
            self.save_config()
            self.update_all_texts()

    def update_all_texts(self):
        """更新所有界面文本"""
        # 更新窗口标题
        self.setWindowTitle(self.get_text('window_title'))

        # 更新选项卡标题
        self.tabs.setTabText(self.monitoring_tab_index, self.get_text('tab_monitoring'))
        self.tabs.setTabText(self.config_tab_index, self.get_text('tab_config'))
        self.tabs.setTabText(self.chart_tab_index, self.get_text('tab_chart'))
        self.tabs.setTabText(self.language_tab_index, self.get_text('tab_language'))

        # 更新基本信息标签
        self.surgeon_label.setText(f"{self.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_label.setText(f"{self.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label.setText(f"{self.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label.setText(f"{self.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label.setText(f"{self.get_text('remote_ip')}: {self.config['remote_ip']}")

        # 更新图表标签
        self.graph_widget.setLabel("left", self.get_text('latency_ms'), color="#2c3e50", size="14pt")
        self.graph_widget.setLabel("bottom", self.get_text('time_s'), color="#2c3e50", size="14pt")

        # 更新GroupBox标题
        self.info_group.setTitle(self.get_text('basic_info'))
        self.network_group.setTitle(self.get_text('network_status'))
        self.chart_group.setTitle(self.get_text('network_history'))
        self.chart_config_group.setTitle(self.get_text('chart_settings'))

        # 更新配置界面标签
        # 清除现有的表单行并重新添加
        while self.config_form_layout.rowCount() > 0:
            self.config_form_layout.removeRow(0)

        self.config_form_layout.addRow(f"{self.get_text('surgeon_location')}:", self.surgeon_location_edit)
        self.config_form_layout.addRow(f"{self.get_text('patient_location')}:", self.patient_location_edit)
        self.config_form_layout.addRow(f"{self.get_text('physical_distance')}:", self.physical_distance_edit)
        self.config_form_layout.addRow(f"{self.get_text('network_distance')}:", self.network_distance_edit)
        self.config_form_layout.addRow(f"{self.get_text('remote_ip')}:", self.remote_ip_edit)

        # 更新按钮文本
        self.save_button.setText(self.get_text('save_config'))

        # 更新图表设置界面
        self.auto_range_checkbox.setText(self.get_text('auto_range'))
        self.manual_label.setText(f"{self.get_text('manual_max')}:")
        self.apply_chart_button.setText(self.get_text('apply_settings'))

        # 更新语言设置界面
        self.chinese_radio.setText(self.get_text('chinese'))
        self.english_radio.setText(self.get_text('english'))

    def on_auto_range_changed(self, state):
        """自动范围选项改变时的处理"""
        self.auto_y_range = state == 2  # Qt.Checked = 2
        self.manual_y_spinbox.setEnabled(not self.auto_y_range)

    def on_manual_y_changed(self, value):
        """手动Y轴最大值改变时的处理"""
        self.manual_y_max = value

    def apply_chart_settings(self):
        """应用图表设置"""
        if not self.auto_y_range:
            self.y_range_max = self.manual_y_max
            self.graph_widget.setYRange(self.y_range_min, self.y_range_max)
            self.graph_widget.setTitle(f"{self.get_text('latency_ms')} - {self.get_text('manual_range')}: {self.y_range_min}-{self.y_range_max}ms",
                                     color="k", size="11pt")

    def update_info_display(self):
        self.surgeon_label.setText(f"{self.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_label.setText(f"{self.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label.setText(f"{self.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label.setText(f"{self.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label.setText(f"{self.get_text('remote_ip')}: {self.config['remote_ip']}")

    def get_expected_latency_range(self):
        """根据网络距离估算预期延迟范围"""
        network_distance = self.config.get('network_distance', '').lower()

        # 尝试从网络距离字符串中提取数字
        import re
        distance_match = re.search(r'(\d+)', network_distance)

        if distance_match:
            distance_km = int(distance_match.group(1))

            # 根据距离估算延迟（粗略估算：光速传播 + 网络设备处理时间）
            # 光速在光纤中约为200,000 km/s，往返时间 = 距离 * 2 / 200,000 * 1000ms
            base_latency = (distance_km * 2 / 200000) * 1000

            if distance_km < 100:
                return 50  # 本地网络
            elif distance_km < 500:
                return 100  # 区域网络
            elif distance_km < 2000:
                return 200  # 国内长距离
            else:
                return 400  # 国际网络

        # 如果无法解析距离，根据描述判断
        if any(word in network_distance for word in ['本地', '局域', 'local', 'lan']):
            return 50
        elif any(word in network_distance for word in ['城市', 'city', '市内']):
            return 100
        elif any(word in network_distance for word in ['国内', 'domestic', '省内']):
            return 200
        elif any(word in network_distance for word in ['国际', 'international', '海外']):
            return 400
        else:
            return 150  # 默认值

    def update_y_range(self):
        """根据当前延迟数据和网络距离动态调整Y轴范围"""
        if not self.ping_data or not self.auto_y_range:
            return

        # 获取当前数据的最大值和最小值
        current_max = max(self.ping_data)
        current_min = min(self.ping_data)

        # 获取基于网络距离的预期范围
        expected_max = self.get_expected_latency_range()

        # 智能选择Y轴最大值
        if current_max <= expected_max * 0.5:
            # 实际延迟远小于预期，使用较小的范围以提高精度
            new_max = max(30, int(current_max * 1.5))
        elif current_max <= expected_max:
            # 实际延迟在预期范围内
            new_max = expected_max
        else:
            # 实际延迟超过预期，可能有网络问题
            new_max = max(expected_max, int(current_max * 1.2))

        # 设置合理的刻度间隔
        if new_max <= 50:
            new_max = ((new_max // 10) + 1) * 10  # 向上取整到10的倍数
        elif new_max <= 200:
            new_max = ((new_max // 25) + 1) * 25  # 向上取整到25的倍数
        else:
            new_max = ((new_max // 50) + 1) * 50  # 向上取整到50的倍数

        # 设置最小值
        if current_min > new_max * 0.3:
            new_min = max(0, int(current_min * 0.8))
        else:
            new_min = 0

        # 只有当范围变化较大时才更新（避免频繁调整）
        if abs(new_max - self.y_range_max) > self.y_range_max * 0.3 or \
           abs(new_min - self.y_range_min) > 10:
            self.y_range_min = new_min
            self.y_range_max = new_max
            self.graph_widget.setYRange(self.y_range_min, self.y_range_max)

            # 更新图表标题以显示当前范围和网络类型
            network_type = self.classify_network_type(current_max)
            range_info = f"{self.get_text('latency_ms')} - {network_type} | {self.get_text('range')}: {self.y_range_min}-{self.y_range_max}ms"
            self.graph_widget.setTitle(range_info, color="k", size="11pt")

    def classify_network_type(self, latency):
        """根据延迟对网络类型进行分类"""
        if latency <= 10:
            return self.get_text('local_network')
        elif latency <= 30:
            return self.get_text('metro_network')
        elif latency <= 80:
            return self.get_text('regional_network')
        elif latency <= 150:
            return self.get_text('domestic_network')
        elif latency <= 300:
            return self.get_text('international_network')
        else:
            return self.get_text('network_error')
    
    def ping_host(self, host):
        """执行ping命令并返回延迟和丢包率"""
        param = '-n' if platform.system().lower() == 'windows' else '-c'
        count = '5'  # 增加ping次数以获得更准确的统计 - 增加ping次数以获得更准确的统计
        command = ['ping', param, count, host]
        try:
            # 使用gbk编码解码Windows ping输出，隐藏控制台窗口
            if platform.system().lower() == 'windows':
                # Windows下隐藏控制台窗口
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
                startupinfo.wShowWindow = subprocess.SW_HIDE
                output = subprocess.check_output(command, encoding='gbk', startupinfo=startupinfo)
            else:
                output = subprocess.check_output(command).decode('utf-8')

            if platform.system().lower() == 'windows':
                # Windows系统下提取延迟和丢包率
                if 'TTL=' in output:
                    # 提取平均延迟 - 修正解析逻辑
                    if '平均 = ' in output:
                        avg_line = output.split('平均 = ')[1].split('ms')[0].strip()
                        latency = float(avg_line)
                    else:
                        # 如果没有统计信息，从最后一个回复中提取延迟
                        times = re.findall(r'时间=(\d+)ms', output)
                        if times:
                            latency = float(times[-1])  # 使用最后一个延迟值
                        else:
                            latency = 0

                    # 提取丢包率 - 修正解析逻辑
                    if '已发送 = ' in output and '已接收 = ' in output:
                        # 提取发送和接收的数据包数量
                        sent_match = re.search(r'已发送 = (\d+)', output)
                        received_match = re.search(r'已接收 = (\d+)', output)

                        if sent_match and received_match:
                            sent = int(sent_match.group(1))
                            received = int(received_match.group(1))
                            loss = ((sent - received) / sent) * 100
                        else:
                            loss = 0
                    else:
                        loss = 0

                    return latency, loss
                else:
                    return None, 100
            else:
                # Linux/Mac系统下提取延迟和丢包率
                if 'received' in output:
                    # 提取平均延迟
                    if 'avg' in output:
                        avg_section = output.split('min/avg/max')[1].split('=')[1].strip()
                        latency = float(avg_section.split('/')[1])
                    else:
                        latency = 0

                    # 提取丢包率
                    packet_stats = output.split(',')[1].strip()
                    received = int(packet_stats.split(' ')[0])
                    loss = 100 - (received / int(count) * 100)

                    return latency, loss
                else:
                    return None, 100
        except Exception as e:
            print(f"Ping error: {e}")  # 添加调试信息
            return None, 100
    
    def update_data(self):
        # 获取当前时间
        current_time = time.time() - self.start_time

        # 异步获取ping数据 - 避免阻塞UI
        if not self.ping_in_progress:
            self.start_async_ping()

        # 检查是否到了更新图表的时间（固定间隔更新）
        if current_time - self.last_update_time >= self.update_interval:
            # 使用缓存的结果更新界面
            latency_data, loss = self.last_ping_result

            # 处理延迟数据（现在是元组或None）
            if latency_data == -1 or latency_data is None:
                current_latency = None
                avg_latency = None
            elif isinstance(latency_data, tuple):
                current_latency, avg_latency = latency_data
            else:
                # 兼容旧格式
                current_latency = latency_data
                avg_latency = latency_data

            # 更新图表数据（使用当前延迟）
            self.update_chart_data(current_latency, loss, current_time)
            self.last_update_time = current_time

        # 始终更新状态显示（不管是否更新图表）
        latency_data, loss = self.last_ping_result

        # 处理延迟数据
        if latency_data == -1 or latency_data is None:
            current_latency = None
            avg_latency = None
        elif isinstance(latency_data, tuple):
            current_latency, avg_latency = latency_data
        else:
            # 兼容旧格式
            current_latency = latency_data
            avg_latency = latency_data

        self.update_status_display(current_latency, avg_latency, loss)

    def update_chart_data(self, latency, loss, current_time):
        """更新图表数据（固定间隔调用）"""
        if latency is not None:
            # 添加新的数据点
            self.ping_data.append(latency)
            self.timestamps.append(current_time)

            # 保持最近120秒的数据（基于时间清理旧数据）
            cutoff_time = current_time - 120  # 保留最近120秒的数据

            # 找到需要保留的数据的起始索引
            keep_from_index = 0
            for i, timestamp in enumerate(self.timestamps):
                if timestamp >= cutoff_time:
                    keep_from_index = i
                    break

            # 清理超过120秒的旧数据
            if keep_from_index > 0:
                self.ping_data = self.ping_data[keep_from_index:]
                self.timestamps = self.timestamps[keep_from_index:]

            # 批量更新图表 - 减少重绘次数
            self.update_chart_display()

            # 动态调整Y轴范围
            self.update_y_range()
        else:
            # ping失败时也要添加数据点，保持时间连续性
            # 使用上一个有效值或0
            last_valid_latency = self.ping_data[-1] if self.ping_data else 0
            self.ping_data.append(last_valid_latency)
            self.timestamps.append(current_time)

            # 更新图表显示
            self.update_chart_display()

    def update_status_display(self, current_latency, avg_latency, loss):
        """更新状态显示（每次都调用）"""
        # 更新网络连接状态和丢包率
        self.total_ping_attempts += 1

        if current_latency is not None:
            self.successful_pings += 1
            # 检查网络是否已连接（连续3次成功ping后认为已连接）
            if self.successful_pings >= 3:
                self.network_connected = True

        # 只有在网络连接后才开始计算丢包率
        if self.network_connected:
            if current_latency is not None:
                # ping成功时，使用实际丢包率进行平滑处理
                self.packet_loss = 0.8 * self.packet_loss + 0.2 * loss
            else:
                # ping失败时，适度增加丢包率
                self.packet_loss = min(100, 0.8 * self.packet_loss + 20)
        else:
            # 网络未连接时，显示连接中状态，丢包率保持为0
            self.packet_loss = 0

        # 更新延迟和丢包率显示
        if current_latency is not None:
            self.latency_label.setText(f"{self.get_text('current_latency')}: {current_latency:.1f} ms")

            # 更新平均延迟显示
            if avg_latency is not None:
                self.avg_latency_label.setText(f"{self.get_text('average_latency')}: {avg_latency:.1f} ms")

            # 更新连接状态指示器
            network_type = self.classify_network_type(current_latency)
            if current_latency < 100:  # 修改为100ms阈值
                self.status_indicator.setText(f"● {self.get_text('connection_status')}: {self.get_text('excellent')} ({network_type})")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        color: #27ae60;
                        background-color: #d5f4e6;
                        padding: 8px;
                        border-radius: 5px;
                        border: 1px solid #27ae60;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                """)
            else:  # 100ms以上为黄色
                self.status_indicator.setText(f"● {self.get_text('connection_status')}: {self.get_text('good')} ({network_type})")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        color: #f39c12;
                        background-color: #fef9e7;
                        padding: 8px;
                        border-radius: 5px;
                        border: 1px solid #f39c12;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                """)

            # 设置美观的状态样式
            if current_latency < 100:  # 修改为100ms阈值
                latency_style = """
                    QLabel {
                        color: #27ae60;
                        background-color: #d5f4e6;
                        padding: 12px;
                        border-radius: 8px;
                        border: 2px solid #27ae60;
                        font-weight: bold;
                        font-size: 13pt;
                    }
                """
            else:
                latency_style = """
                    QLabel {
                        color: #f39c12;
                        background-color: #fef9e7;
                        padding: 12px;
                        border-radius: 8px;
                        border: 2px solid #f39c12;
                        font-weight: bold;
                        font-size: 13pt;
                    }
                """
            self.latency_label.setStyleSheet(latency_style)
            # 为平均延迟标签设置相同的样式
            self.avg_latency_label.setStyleSheet(latency_style)
        else:
            self.latency_label.setText(f"{self.get_text('current_latency')}: {self.get_text('timeout')}")
            self.avg_latency_label.setText(f"{self.get_text('average_latency')}: {self.get_text('timeout')}")
            timeout_style = """
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #e74c3c;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
            self.latency_label.setStyleSheet(timeout_style)
            self.avg_latency_label.setStyleSheet(timeout_style)

            # 更新连接状态为断开
            self.status_indicator.setText(f"● {self.get_text('connection_status')}: {self.get_text('failed')}")
            self.status_indicator.setStyleSheet("""
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 8px;
                    border-radius: 5px;
                    border: 1px solid #e74c3c;
                    font-size: 12pt;
                    font-weight: bold;
                }
            """)

        # 更新丢包率显示
        self.packet_loss_label.setText(f"{self.get_text('packet_loss')}: {self.packet_loss:.1f}%")

        # 设置美观的丢包率状态样式
        if self.packet_loss < 1:
            loss_style = """
                QLabel {
                    color: #27ae60;
                    background-color: #d5f4e6;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #27ae60;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        elif self.packet_loss < 5:
            loss_style = """
                QLabel {
                    color: #f39c12;
                    background-color: #fef9e7;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #f39c12;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        else:
            loss_style = """
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #e74c3c;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        self.packet_loss_label.setStyleSheet(loss_style)



    def start_async_ping(self):
        """启动异步ping"""
        if self.ping_in_progress:
            return

        self.ping_in_progress = True
        worker = PingWorker(self.config['remote_ip'])
        worker.signals.finished.connect(self.on_ping_finished)
        worker.signals.error.connect(self.on_ping_error)
        self.thread_pool.start(worker)

    def on_ping_finished(self, latency_data, loss):
        """ping完成回调"""
        self.ping_in_progress = False
        # latency_data现在是(current_latency, avg_latency)元组或-1
        if latency_data == -1:
            self.last_ping_result = (None, loss)
        else:
            self.last_ping_result = (latency_data, loss)

    def on_ping_error(self):
        """ping错误回调"""
        self.ping_in_progress = False
        self.last_ping_result = (None, 100)

    def update_chart_display(self):
        """批量更新图表显示，减少重绘次数"""
        if not self.ping_data:
            return

        # 更新主曲线
        self.curve.setData(self.timestamps, self.ping_data)

        # 更新填充区域
        self.fill_curve.setData(self.timestamps, self.ping_data)

        # 更新阈值线（只在有数据时）
        if len(self.timestamps) > 1:
            self.threshold_line_120.setData([self.timestamps[0], self.timestamps[-1]], [120, 120])
            self.threshold_line_150.setData([self.timestamps[0], self.timestamps[-1]], [150, 150])

        # 自动调整X轴范围，确保始终显示最新数据
        if len(self.timestamps) > 1:
            # 使用基于时间的窗口，显示最近60秒的数据
            current_time = self.timestamps[-1]
            time_window = 60  # 显示最近60秒的数据

            if current_time <= time_window:
                # 程序运行时间少于60秒，显示全部
                x_min = max(0, self.timestamps[0] - 5)  # 左边留5秒边距
                x_max = current_time + 5  # 右边留5秒边距
            else:
                # 程序运行超过60秒，显示最近60秒的滚动窗口
                x_min = current_time - time_window
                x_max = current_time + 5  # 右边留5秒边距

            # 设置X轴范围，确保图表实时滚动
            self.graph_widget.setXRange(x_min, x_max, padding=0)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = RemoteSurgeryInterface()
    window.show()
    sys.exit(app.exec_())



