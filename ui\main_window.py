#!/usr/bin/env python
# -*- coding: utf-8 -*-

import time
from PyQt5.QtWidgets import (QMainWindow, QWidget, QVBoxLayout,
                            QHBoxLayout, QLabel, QLineEdit, QPushButton,
                            QTabWidget, QGridLayout, QGroupBox, QFormLayout,
                            QRadioButton, QSpinBox, QCheckBox)
from PyQt5.QtCore import QTimer, Qt, QThreadPool
from PyQt5.QtGui import QFont
import pyqtgraph as pg

from network.ping_worker import PingWorker
from utils.config_manager import load_config, save_config
from utils.language_manager import LanguageManager
from ui.chart_manager import ChartManager

class RemoteSurgeryInterface(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 初始化数据
        self.config = load_config()
        self.language_manager = LanguageManager()
        self.language_manager.current_language = self.config.get('language', 'zh')
        
        self.packet_loss = 0
        
        # 网络连接状态管理
        self.network_connected = False
        self.successful_pings = 0
        self.total_ping_attempts = 0
        
        # 时间管理
        self.start_time = time.time()  # 记录程序启动时间
        
        # 设置窗口标题和大小
        self.setWindowTitle(self.language_manager.get_text('window_title'))
        self.setMinimumSize(1200, 800)
        
        # 设置主窗口样式
        self.setup_styles()
        
        # 创建主界面
        self.init_ui()
        
        # 初始化线程池用于异步ping
        self.thread_pool = QThreadPool()
        self.thread_pool.setMaxThreadCount(1)  # 限制为单线程避免并发问题
        
        # ping状态管理
        self.ping_in_progress = False
        self.last_ping_result = (None, 100)  # 缓存上次结果
        
        # 数据刷新管理
        self.last_update_time = 0  # 上次更新图表的时间
        self.update_interval = 3  # 固定3秒更新间隔
        
        # 设置定时器更新数据
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_data)
        self.timer.start(500)  # 每0.5秒检查一次，但图表按固定间隔更新
    
    def setup_styles(self):
        """设置样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 2px solid #3498db;
                border-radius: 10px;
                background-color: #ffffff;
                margin-top: 5px;
            }
            QTabWidget::tab-bar {
                alignment: left;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                color: #2c3e50;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 10pt;
                font-weight: bold;
                min-width: 140px;
                max-width: 180px;
                min-height: 40px;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)
    
    def init_ui(self):
        """初始化UI"""
        # 创建中央部件和主布局
        central_widget = QWidget()
        main_layout = QVBoxLayout(central_widget)
        
        # 创建选项卡
        tabs = QTabWidget()
        main_layout.addWidget(tabs)
        
        # 创建监控界面
        monitoring_tab = QWidget()
        self.monitoring_tab_index = tabs.addTab(monitoring_tab, self.language_manager.get_text('tab_monitoring'))

        # 创建配置界面
        config_tab = QWidget()
        self.config_tab_index = tabs.addTab(config_tab, self.language_manager.get_text('tab_config'))

        # 创建图表设置界面
        chart_config_tab = QWidget()
        self.chart_tab_index = tabs.addTab(chart_config_tab, self.language_manager.get_text('tab_chart'))

        # 创建语言设置界面
        language_tab = QWidget()
        self.language_tab_index = tabs.addTab(language_tab, self.language_manager.get_text('tab_language'))

        # 设置各个界面
        self.setup_monitoring_tab(monitoring_tab)
        self.setup_config_tab(config_tab)
        self.setup_chart_config_tab(chart_config_tab)
        self.setup_language_tab(language_tab)

        # 保存tabs引用以便更新
        self.tabs = tabs
        
        self.setCentralWidget(central_widget)
    
    def setup_monitoring_tab(self, tab):
        """设置监控界面"""
        layout = QVBoxLayout(tab)
        
        # 信息显示区域
        self.info_group = QGroupBox(self.language_manager.get_text('basic_info'))
        self.info_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        info_layout = QGridLayout()
        
        # 创建信息标签
        self.surgeon_location_label = QLabel(f"{self.language_manager.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_location_label = QLabel(f"{self.language_manager.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label = QLabel(f"{self.language_manager.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label = QLabel(f"{self.language_manager.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label = QLabel(f"{self.language_manager.get_text('remote_ip')}: {self.config['remote_ip']}")
        
        # 设置标签样式
        info_style = """
            QLabel {
                font-size: 12pt;
                color: #2c3e50;
                padding: 5px;
            }
        """
        self.surgeon_location_label.setStyleSheet(info_style)
        self.patient_location_label.setStyleSheet(info_style)
        self.physical_distance_label.setStyleSheet(info_style)
        self.network_distance_label.setStyleSheet(info_style)
        self.remote_ip_label.setStyleSheet(info_style)
        
        # 添加到布局
        info_layout.addWidget(self.surgeon_location_label, 0, 0)
        info_layout.addWidget(self.patient_location_label, 0, 1)
        info_layout.addWidget(self.physical_distance_label, 1, 0)
        info_layout.addWidget(self.network_distance_label, 1, 1)
        info_layout.addWidget(self.remote_ip_label, 2, 0, 1, 2)
        
        self.info_group.setLayout(info_layout)
        layout.addWidget(self.info_group)
        
        # 网络状态显示区域
        self.network_group = QGroupBox(self.language_manager.get_text('network_status'))
        self.network_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #e74c3c;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        network_layout = QVBoxLayout()
        
        # 创建网络状态标签
        self.latency_label = QLabel(f"{self.language_manager.get_text('current_latency')}: -- ms")
        self.avg_latency_label = QLabel(f"{self.language_manager.get_text('average_latency')}: -- ms")
        self.packet_loss_label = QLabel(f"{self.language_manager.get_text('packet_loss')}: 0%")
        self.status_indicator = QLabel(f"● {self.language_manager.get_text('connection_status')}: {self.language_manager.get_text('connecting')}...")
        
        # 设置标签样式
        self.latency_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 12px;
                border-radius: 8px;
                border: 2px solid #7f8c8d;
                font-weight: bold;
                font-size: 13pt;
            }
        """)
        self.avg_latency_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 12px;
                border-radius: 8px;
                border: 2px solid #7f8c8d;
                font-weight: bold;
                font-size: 13pt;
            }
        """)
        self.packet_loss_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                background-color: #d5f4e6;
                padding: 12px;
                border-radius: 8px;
                border: 2px solid #27ae60;
                font-weight: bold;
                font-size: 13pt;
            }
        """)
        self.status_indicator.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                background-color: #ecf0f1;
                padding: 8px;
                border-radius: 5px;
                border: 1px solid #7f8c8d;
                font-size: 12pt;
                font-weight: bold;
            }
        """)
        
        # 添加到布局
        network_layout.addWidget(self.latency_label)
        network_layout.addWidget(self.avg_latency_label)
        network_layout.addWidget(self.packet_loss_label)
        network_layout.addWidget(self.status_indicator)
        
        self.network_group.setLayout(network_layout)
        layout.addWidget(self.network_group)
        
        # 图表区域
        self.graph_group = QGroupBox(self.language_manager.get_text('network_history'))
        self.graph_group.setStyleSheet("""
            QGroupBox {
                font-size: 16pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        graph_layout = QVBoxLayout()
        
        # 创建图表
        self.graph_widget = pg.PlotWidget()
        graph_layout.addWidget(self.graph_widget)
        
        self.graph_group.setLayout(graph_layout)
        layout.addWidget(self.graph_group)
        
        # 初始化图表管理器
        self.chart_manager = ChartManager(self.graph_widget, self.language_manager)
    
    def setup_config_tab(self, tab):
        """设置配置界面"""
        layout = QVBoxLayout(tab)
        
        # 创建表单
        form_layout = QFormLayout()
        
        # 创建输入框
        self.surgeon_location_input = QLineEdit(self.config['surgeon_location'])
        self.patient_location_input = QLineEdit(self.config['patient_location'])
        self.physical_distance_input = QLineEdit(self.config['physical_distance'])
        self.network_distance_input = QLineEdit(self.config['network_distance'])
        self.remote_ip_input = QLineEdit(self.config['remote_ip'])
        
        # 设置输入框样式
        input_style = """
            QLineEdit {
                padding: 8px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12pt;
            }
            QLineEdit:focus {
                border: 2px solid #3498db;
            }
        """
        self.surgeon_location_input.setStyleSheet(input_style)
        self.patient_location_input.setStyleSheet(input_style)
        self.physical_distance_input.setStyleSheet(input_style)
        self.network_distance_input.setStyleSheet(input_style)
        self.remote_ip_input.setStyleSheet(input_style)
        
        # 添加到表单
        self.surgeon_location_label_form = QLabel(self.language_manager.get_text('surgeon_location'))
        self.patient_location_label_form = QLabel(self.language_manager.get_text('patient_location'))
        self.physical_distance_label_form = QLabel(self.language_manager.get_text('physical_distance'))
        self.network_distance_label_form = QLabel(self.language_manager.get_text('network_distance'))
        self.remote_ip_label_form = QLabel(self.language_manager.get_text('remote_ip'))
        
        form_layout.addRow(self.surgeon_location_label_form, self.surgeon_location_input)
        form_layout.addRow(self.patient_location_label_form, self.patient_location_input)
        form_layout.addRow(self.physical_distance_label_form, self.physical_distance_input)
        form_layout.addRow(self.network_distance_label_form, self.network_distance_input)
        form_layout.addRow(self.remote_ip_label_form, self.remote_ip_input)
        
        # 创建保存按钮
        self.save_button = QPushButton(self.language_manager.get_text('save_config'))
        self.save_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.save_button.clicked.connect(self.save_config)
        
        # 添加到布局
        layout.addLayout(form_layout)
        layout.addWidget(self.save_button, alignment=Qt.AlignCenter)
    
    def setup_chart_config_tab(self, tab):
        """设置图表配置界面"""
        layout = QVBoxLayout(tab)
        
        # 创建Y轴范围设置组
        self.y_range_group = QGroupBox(self.language_manager.get_text('chart_settings'))
        self.y_range_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        y_range_layout = QVBoxLayout()
        
        # 自动调整Y轴范围选项
        self.auto_y_range_checkbox = QCheckBox(self.language_manager.get_text('auto_range'))
        self.auto_y_range_checkbox.setChecked(self.chart_manager.auto_y_range)
        self.auto_y_range_checkbox.setStyleSheet("""
            QCheckBox {
                font-size: 12pt;
                color: #2c3e50;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
            }
        """)
        self.auto_y_range_checkbox.stateChanged.connect(self.toggle_auto_y_range)
        
        # 手动设置Y轴最大值
        manual_layout = QHBoxLayout()
        self.manual_y_max_label = QLabel(self.language_manager.get_text('manual_max'))
        self.manual_y_max_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #2c3e50;
            }
        """)
        self.manual_y_max_spinbox = QSpinBox()
        self.manual_y_max_spinbox.setRange(50, 1000)
        self.manual_y_max_spinbox.setSingleStep(50)
        self.manual_y_max_spinbox.setValue(self.chart_manager.manual_y_max)
        self.manual_y_max_spinbox.setEnabled(not self.chart_manager.auto_y_range)
        self.manual_y_max_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 5px;
                border: 1px solid #bdc3c7;
                border-radius: 5px;
                font-size: 12pt;
            }
        """)
        manual_layout.addWidget(self.manual_y_max_label)
        manual_layout.addWidget(self.manual_y_max_spinbox)
        
        # 应用设置按钮
        self.apply_settings_button = QPushButton(self.language_manager.get_text('apply_settings'))
        self.apply_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:pressed {
                background-color: #1c6ea4;
            }
        """)
        self.apply_settings_button.clicked.connect(self.apply_chart_settings)
        
        # 添加到布局
        y_range_layout.addWidget(self.auto_y_range_checkbox)
        y_range_layout.addLayout(manual_layout)
        y_range_layout.addWidget(self.apply_settings_button, alignment=Qt.AlignCenter)
        
        self.y_range_group.setLayout(y_range_layout)
        layout.addWidget(self.y_range_group)
        layout.addStretch()
    
    def setup_language_tab(self, tab):
        """设置语言界面"""
        layout = QVBoxLayout(tab)
        
        # 创建语言设置组
        self.language_group = QGroupBox(self.language_manager.get_text('language_settings'))
        self.language_group.setStyleSheet("""
            QGroupBox {
                font-size: 14pt;
                font-weight: bold;
                color: #2c3e50;
                border: 2px solid #3498db;
                border-radius: 10px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: #f8f9fa;
            }
        """)
        language_layout = QVBoxLayout()
        
        # 语言选择标签
        self.select_language_label = QLabel(self.language_manager.get_text('select_language'))
        self.select_language_label.setStyleSheet("""
            QLabel {
                font-size: 12pt;
                color: #2c3e50;
                margin-bottom: 10px;
            }
        """)
        
        # 中文选项
        self.chinese_radio = QRadioButton(self.language_manager.get_text('chinese'))
        self.chinese_radio.setStyleSheet("""
            QRadioButton {
                font-size: 12pt;
                color: #2c3e50;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
            }
        """)
        
        # 英文选项
        self.english_radio = QRadioButton(self.language_manager.get_text('english'))
        self.english_radio.setStyleSheet("""
            QRadioButton {
                font-size: 12pt;
                color: #2c3e50;
            }
            QRadioButton::indicator {
                width: 20px;
                height: 20px;
            }
        """)
        
        # 设置当前语言
        if self.language_manager.current_language == 'zh':
            self.chinese_radio.setChecked(True)
        else:
            self.english_radio.setChecked(True)
        
        # 连接信号
        self.chinese_radio.toggled.connect(lambda checked: self.change_language('zh') if checked else None)
        self.english_radio.toggled.connect(lambda checked: self.change_language('en') if checked else None)
        
        # 添加到布局
        language_layout.addWidget(self.select_language_label)
        language_layout.addWidget(self.chinese_radio)
        language_layout.addWidget(self.english_radio)
        
        self.language_group.setLayout(language_layout)
        layout.addWidget(self.language_group)
        layout.addStretch()
    
    def update_data(self):
        """更新数据（定时器触发）"""
        # 检查是否需要更新图表（固定间隔）
        current_time = time.time() - self.start_time
        time_since_last_update = current_time - self.last_update_time
        
        # 如果没有ping在进行中且达到更新间隔，启动新的ping
        if not self.ping_in_progress and time_since_last_update >= self.update_interval:
            self.ping_in_progress = True
            self.last_update_time = current_time
            
            # 创建ping工作线程
            worker = PingWorker(self.config['remote_ip'])
            worker.signals.finished.connect(self.on_ping_finished)
            worker.signals.error.connect(self.on_ping_error)
            
            # 启动ping
            self.thread_pool.start(worker)
            
            # 更新图表（使用上次的结果）
            latency_data, loss = self.last_ping_result
            if latency_data is not None and latency_data != -1:
                current_latency, avg_latency = latency_data
                self.update_status_display(current_latency, avg_latency, loss)
                self.chart_manager.update_chart_data(current_latency, loss, current_time)
            else:
                self.update_status_display(None, None, loss)
                self.chart_manager.update_chart_data(None, loss, current_time)
    
    def on_ping_finished(self, latency_data, loss):
        """ping完成回调"""
        self.ping_in_progress = False
        
        if latency_data == -1:
            self.last_ping_result = (None, loss)
        else:
            self.last_ping_result = (latency_data, loss)
    
    def on_ping_error(self):
        """ping错误回调"""
        self.ping_in_progress = False
        self.last_ping_result = (None, 100)
    
    def update_status_display(self, current_latency, avg_latency, loss):
        """更新状态显示（每次都调用）"""
        # 更新网络连接状态和丢包率
        self.total_ping_attempts += 1

        if current_latency is not None:
            self.successful_pings += 1
            # 检查网络是否已连接（连续3次成功ping后认为已连接）
            if self.successful_pings >= 3:
                self.network_connected = True

        # 只有在网络连接后才开始计算丢包率
        if self.network_connected:
            if current_latency is not None:
                # ping成功时，使用实际丢包率进行平滑处理
                self.packet_loss = 0.8 * self.packet_loss + 0.2 * loss
            else:
                # ping失败时，适度增加丢包率
                self.packet_loss = min(100, 0.8 * self.packet_loss + 20)
        else:
            # 网络未连接时，显示连接中状态，丢包率保持为0
            self.packet_loss = 0

        # 更新延迟和丢包率显示
        if current_latency is not None:
            self.latency_label.setText(f"{self.language_manager.get_text('current_latency')}: {current_latency:.1f} ms")

            # 更新平均延迟显示
            if avg_latency is not None:
                self.avg_latency_label.setText(f"{self.language_manager.get_text('average_latency')}: {avg_latency:.1f} ms")

            # 更新连接状态指示器
            network_type = self.classify_network_type(current_latency)
            if current_latency < 100:  # 100ms以下为绿色
                self.status_indicator.setText(f"● {self.language_manager.get_text('connection_status')}: {self.language_manager.get_text('excellent')} ({network_type})")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        color: #27ae60;
                        background-color: #d5f4e6;
                        padding: 8px;
                        border-radius: 5px;
                        border: 1px solid #27ae60;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                """)
            else:  # 100ms以上为黄色
                self.status_indicator.setText(f"● {self.language_manager.get_text('connection_status')}: {self.language_manager.get_text('good')} ({network_type})")
                self.status_indicator.setStyleSheet("""
                    QLabel {
                        color: #f39c12;
                        background-color: #fef9e7;
                        padding: 8px;
                        border-radius: 5px;
                        border: 1px solid #f39c12;
                        font-size: 12pt;
                        font-weight: bold;
                    }
                """)

            # 设置美观的状态样式
            if current_latency < 100:  # 100ms以下为绿色
                latency_style = """
                    QLabel {
                        color: #27ae60;
                        background-color: #d5f4e6;
                        padding: 12px;
                        border-radius: 8px;
                        border: 2px solid #27ae60;
                        font-weight: bold;
                        font-size: 13pt;
                    }
                """
            else:
                latency_style = """
                    QLabel {
                        color: #f39c12;
                        background-color: #fef9e7;
                        padding: 12px;
                        border-radius: 8px;
                        border: 2px solid #f39c12;
                        font-weight: bold;
                        font-size: 13pt;
                    }
                """
            self.latency_label.setStyleSheet(latency_style)
            # 为平均延迟标签设置相同的样式
            self.avg_latency_label.setStyleSheet(latency_style)
        else:
            # ping失败时的显示
            self.latency_label.setText(f"{self.language_manager.get_text('current_latency')}: Timeout")
            self.avg_latency_label.setText(f"{self.language_manager.get_text('average_latency')}: --")
            self.status_indicator.setText(f"● {self.language_manager.get_text('connection_status')}: {self.language_manager.get_text('poor')}")
            
            # 设置超时样式
            timeout_style = """
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #e74c3c;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
            self.latency_label.setStyleSheet(timeout_style)
            self.avg_latency_label.setStyleSheet(timeout_style)
            self.status_indicator.setStyleSheet("""
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 8px;
                    border-radius: 5px;
                    border: 1px solid #e74c3c;
                    font-size: 12pt;
                    font-weight: bold;
                }
            """)

        # 更新丢包率显示
        self.packet_loss_label.setText(f"{self.language_manager.get_text('packet_loss')}: {self.packet_loss:.1f}%")
        
        # 设置丢包率样式
        if self.packet_loss < 1:
            packet_loss_style = """
                QLabel {
                    color: #27ae60;
                    background-color: #d5f4e6;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #27ae60;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        elif self.packet_loss < 5:
            packet_loss_style = """
                QLabel {
                    color: #f39c12;
                    background-color: #fef9e7;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #f39c12;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        else:
            packet_loss_style = """
                QLabel {
                    color: #e74c3c;
                    background-color: #fadbd8;
                    padding: 12px;
                    border-radius: 8px;
                    border: 2px solid #e74c3c;
                    font-weight: bold;
                    font-size: 13pt;
                }
            """
        self.packet_loss_label.setStyleSheet(packet_loss_style)
    
    def classify_network_type(self, latency):
        """根据延迟分类网络类型"""
        if latency < 10:
            return self.language_manager.get_text('wired')
        elif latency < 50:
            return self.language_manager.get_text('wireless')
        elif latency < 100:
            return self.language_manager.get_text('mobile')
        elif latency < 500:
            return self.language_manager.get_text('satellite')
        else:
            return self.language_manager.get_text('unknown')
    
    def save_config(self):
        """保存配置"""
        # 更新配置
        self.config['surgeon_location'] = self.surgeon_location_input.text()
        self.config['patient_location'] = self.patient_location_input.text()
        self.config['physical_distance'] = self.physical_distance_input.text()
        self.config['network_distance'] = self.network_distance_input.text()
        self.config['remote_ip'] = self.remote_ip_input.text()
        
        # 保存到文件
        save_config(self.config)
        
        # 更新显示
        self.surgeon_location_label.setText(f"{self.language_manager.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_location_label.setText(f"{self.language_manager.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label.setText(f"{self.language_manager.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label.setText(f"{self.language_manager.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label.setText(f"{self.language_manager.get_text('remote_ip')}: {self.config['remote_ip']}")
    
    def toggle_auto_y_range(self, state):
        """切换自动Y轴范围"""
        self.manual_y_max_spinbox.setEnabled(not state)
        self.chart_manager.set_auto_y_range(state)
    
    def apply_chart_settings(self):
        """应用图表设置"""
        if not self.auto_y_range_checkbox.isChecked():
            self.chart_manager.set_manual_y_range(self.manual_y_max_spinbox.value())
        else:
            self.chart_manager.set_auto_y_range(True)
    
    def change_language(self, language):
        """切换语言"""
        if language == self.language_manager.current_language:
            return
        
        # 更新语言
        self.language_manager.set_language(language)
        self.config['language'] = language
        save_config(self.config)
        
        # 更新界面文本
        self.update_all_texts()
    
    def update_all_texts(self):
        """更新所有文本"""
        # 更新窗口标题
        self.setWindowTitle(self.language_manager.get_text('window_title'))
        
        # 更新选项卡标题
        self.tabs.setTabText(self.monitoring_tab_index, self.language_manager.get_text('tab_monitoring'))
        self.tabs.setTabText(self.config_tab_index, self.language_manager.get_text('tab_config'))
        self.tabs.setTabText(self.chart_tab_index, self.language_manager.get_text('tab_chart'))
        self.tabs.setTabText(self.language_tab_index, self.language_manager.get_text('tab_language'))
        
        # 更新监控界面
        self.info_group.setTitle(self.language_manager.get_text('basic_info'))
        self.surgeon_location_label.setText(f"{self.language_manager.get_text('surgeon_location')}: {self.config['surgeon_location']}")
        self.patient_location_label.setText(f"{self.language_manager.get_text('patient_location')}: {self.config['patient_location']}")
        self.physical_distance_label.setText(f"{self.language_manager.get_text('physical_distance')}: {self.config['physical_distance']}")
        self.network_distance_label.setText(f"{self.language_manager.get_text('network_distance')}: {self.config['network_distance']}")
        self.remote_ip_label.setText(f"{self.language_manager.get_text('remote_ip')}: {self.config['remote_ip']}")
        
        self.network_group.setTitle(self.language_manager.get_text('network_status'))
        self.latency_label.setText(f"{self.language_manager.get_text('current_latency')}: -- ms")
        self.avg_latency_label.setText(f"{self.language_manager.get_text('average_latency')}: -- ms")
        self.packet_loss_label.setText(f"{self.language_manager.get_text('packet_loss')}: {self.packet_loss:.1f}%")
        self.status_indicator.setText(f"● {self.language_manager.get_text('connection_status')}: {self.language_manager.get_text('connecting')}...")
        
        self.graph_group.setTitle(self.language_manager.get_text('network_history'))
        self.graph_widget.setLabel('left', self.language_manager.get_text('latency_ms'))
        self.graph_widget.setLabel('bottom', self.language_manager.get_text('time_s'))
        
        # 更新配置界面
        self.surgeon_location_label_form.setText(self.language_manager.get_text('surgeon_location'))
        self.patient_location_label_form.setText(self.language_manager.get_text('patient_location'))
        self.physical_distance_label_form.setText(self.language_manager.get_text('physical_distance'))
        self.network_distance_label_form.setText(self.language_manager.get_text('network_distance'))
        self.remote_ip_label_form.setText(self.language_manager.get_text('remote_ip'))
        self.save_button.setText(self.language_manager.get_text('save_config'))
        
        # 更新图表设置界面
        self.y_range_group.setTitle(self.language_manager.get_text('chart_settings'))
        self.auto_y_range_checkbox.setText(self.language_manager.get_text('auto_range'))
        self.manual_y_max_label.setText(self.language_manager.get_text('manual_max'))
        self.apply_settings_button.setText(self.language_manager.get_text('apply_settings'))
        
        # 更新语言设置界面
        self.language_group.setTitle(self.language_manager.get_text('language_settings'))
        self.select_language_label.setText(self.language_manager.get_text('select_language'))
        self.chinese_radio.setText(self.language_manager.get_text('chinese'))
        self.english_radio.setText(self.language_manager.get_text('english'))
