@echo off
chcp 65001 >nul
echo 🚀 康多远程手术监控系统 - 无控制台打包
echo ==========================================

echo 📋 检查Python环境...
python --version
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    pause
    exit /b 1
)

echo 📦 检查PyInstaller...
python -c "import PyInstaller; print('✅ PyInstaller已安装')" 2>nul
if errorlevel 1 (
    echo ❌ PyInstaller未安装，正在安装...
    pip install pyinstaller
)

echo 🧹 清理之前的构建文件...
if exist "build" rmdir /s /q build
if exist "dist" rmdir /s /q dist
if exist "*.spec" del /q *.spec

echo 🔨 开始无控制台打包...
echo 🔧 使用以下参数:
echo   --onefile        : 打包成单个exe文件
echo   --windowed       : GUI程序，不显示控制台
echo   --noconsole      : 完全禁用控制台
echo   --add-data       : 包含必要的数据文件
echo   --hidden-import  : 包含隐藏的导入模块

pyinstaller ^
    --onefile ^
    --windowed ^
    --noconsole ^
    --name="康多远程手术监控系统" ^
    --hidden-import=PyQt5.QtCore ^
    --hidden-import=PyQt5.QtGui ^
    --hidden-import=PyQt5.QtWidgets ^
    --hidden-import=pyqtgraph ^
    --hidden-import=numpy ^
    --hidden-import=subprocess ^
    --hidden-import=platform ^
    --hidden-import=re ^
    --hidden-import=json ^
    --hidden-import=time ^
    --exclude-module=matplotlib ^
    --exclude-module=pandas ^
    --exclude-module=scipy ^
    remote_surgery_interface.py

if exist "dist\康多远程手术监控系统.exe" (
    echo ✅ 打包成功!
    echo 📁 生成的文件: dist\康多远程手术监控系统.exe
    
    echo 📏 文件信息:
    dir "dist\康多远程手术监控系统.exe"
    
    echo.
    echo 🎉 无控制台打包完成! 
    echo 💡 特点:
    echo   ✅ 不会弹出cmd窗口
    echo   ✅ 纯GUI程序体验
    echo   ✅ 适合最终用户使用
    echo   ✅ 可在没有Python环境的电脑上运行
    
    echo.
    echo 🧪 测试建议:
    echo   1. 双击运行exe文件
    echo   2. 检查是否有cmd弹窗
    echo   3. 验证所有功能正常
    echo   4. 在干净的Windows系统上测试
) else (
    echo ❌ 打包失败，请检查错误信息
    echo 💡 常见问题:
    echo   - 检查是否有杀毒软件干扰
    echo   - 确保所有依赖包已安装
    echo   - 检查文件路径是否包含中文
)

echo.
pause
