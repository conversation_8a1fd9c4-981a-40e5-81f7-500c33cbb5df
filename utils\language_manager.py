#!/usr/bin/env python
# -*- coding: utf-8 -*-

class LanguageManager:
    """语言管理类"""
    
    def __init__(self):
        self.languages = self._init_languages()
        self.current_language = 'zh'  # 默认中文
        
    def _init_languages(self):
        """初始化语言包"""
        return {
            'zh': {
                'window_title': '康多远程手术机器人监控系统',
                'tab_monitoring': '网络监控',
                'tab_config': '系统配置',
                'tab_chart': '图表设置',
                'tab_language': '语言设置',
                'basic_info': '基本信息',
                'surgeon_location': '术者位置',
                'patient_location': '患者位置',
                'physical_distance': '物理距离',
                'network_distance': '通讯距离',
                'remote_ip': '远程IP',
                'network_status': '网络状态',
                'current_latency': '当前延迟',
                'average_latency': '平均延迟',
                'packet_loss': '丢包率',
                'connection_status': '连接状态',
                'network_history': '网络延迟历史',
                'latency_ms': '延迟 (ms)',
                'time_s': '时间 (秒)',
                'save_config': '保存配置',
                'chart_settings': 'Y轴范围设置',
                'auto_range': '自动调整Y轴范围',
                'manual_max': '手动设置最大值',
                'apply_settings': '应用设置',
                'language_settings': '语言设置',
                'select_language': '选择语言',
                'chinese': '中文',
                'english': '英语',
                'excellent': '优秀',
                'good': '良好',
                'poor': '较差',
                'failed': '连接失败',
                'timeout': '超时',
                'disconnected': '已断开',
                'connecting': '连接中',
                'dedicated': '专线网络',
                'international': '国际专线',
                'unknown': '未知网络',
                'manual_range': '手动范围'
            },
            'en': {
                'window_title': 'Kangduo Remote Surgery Robot Monitoring System',
                'tab_monitoring': 'Monitor',
                'tab_config': 'Config',
                'tab_chart': 'Chart',
                'tab_language': 'Language',
                'basic_info': 'Basic Information',
                'surgeon_location': 'Surgeon Location',
                'patient_location': 'Patient Location',
                'physical_distance': 'Physical Distance',
                'network_distance': 'Network Distance',
                'remote_ip': 'Remote IP',
                'network_status': 'Network Status',
                'current_latency': 'Current Latency',
                'average_latency': 'Average Latency',
                'packet_loss': 'Packet Loss',
                'connection_status': 'Connection Status',
                'network_history': 'Network Latency History',
                'latency_ms': 'Latency (ms)',
                'time_s': 'Time (s)',
                'save_config': 'Save Config',
                'chart_settings': 'Y-Axis Range Settings',
                'auto_range': 'Auto Y-Axis Range',
                'manual_max': 'Manual Maximum',
                'apply_settings': 'Apply Settings',
                'language_settings': 'Language Settings',
                'select_language': 'Select Language',
                'chinese': 'Chinese',
                'english': 'English',
                'excellent': 'Excellent',
                'good': 'Good',
                'poor': 'Poor',
                'failed': 'Connection Failed',
                'timeout': 'Timeout',
                'disconnected': 'Disconnected',
                'connecting': 'Connecting',
                'dedicated': 'Dedicated',
                'international': 'International',
                'unknown': 'Unknown',
                'manual_range': 'Manual Range'
            }
        }
    
    def set_language(self, language):
        """设置当前语言"""
        if language in self.languages:
            self.current_language = language
            
    def get_text(self, key):
        """获取当前语言的文本"""
        return self.languages[self.current_language].get(key, key)